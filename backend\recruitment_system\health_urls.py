from django.urls import path
from django.http import JsonResponse
from django.db import connection
import time

def health_check(request):
    """健康检查接口"""
    try:
        # 检查数据库连接
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        
        return JsonResponse({
            'status': 'OK',
            'timestamp': time.time(),
            'database': 'connected',
            'message': '系统运行正常'
        })
    except Exception as e:
        return JsonResponse({
            'status': 'ERROR',
            'timestamp': time.time(),
            'database': 'disconnected',
            'error': str(e)
        }, status=500)

urlpatterns = [
    path('', health_check, name='health_check'),
]
