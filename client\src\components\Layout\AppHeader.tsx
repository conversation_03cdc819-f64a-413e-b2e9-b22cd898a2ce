import React from 'react';
import { Layout, Menu, Dropdown, Avatar, Space, Typography } from 'antd';
import {
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
  DashboardOutlined,
  BankOutlined,
  StarOutlined,
  Bar<PERSON>hartOutlined,
  ControlOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { UserRole } from '../../types';

const { Header } = Layout;
const { Text } = Typography;

const AppHeader: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();

  // 用户下拉菜单
  const userMenu = (
    <Menu>
      <Menu.Item key="profile" icon={<UserOutlined />} onClick={() => navigate('/profile')}>
        个人资料
      </Menu.Item>
      <Menu.Item key="settings" icon={<SettingOutlined />}>
        设置
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="logout" icon={<LogoutOutlined />} onClick={logout}>
        退出登录
      </Menu.Item>
    </Menu>
  );

  // 主导航菜单项
  const getMenuItems = () => {
    const items = [
      {
        key: '/dashboard',
        icon: <DashboardOutlined />,
        label: '仪表板',
      },
      {
        key: '/jobs',
        icon: <BankOutlined />,
        label: '岗位信息',
      },
      {
        key: '/recommendations',
        icon: <StarOutlined />,
        label: '智能推荐',
      },
      {
        key: '/analytics',
        icon: <BarChartOutlined />,
        label: '数据分析',
      },
    ];

    // 管理员和HR可以看到管理面板
    if (user?.role === UserRole.ADMIN || user?.role === UserRole.HR) {
      items.push({
        key: '/admin',
        icon: <ControlOutlined />,
        label: '管理面板',
      });
    }

    return items;
  };

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <Header className="app-header" style={{ padding: '0 24px', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
      {/* 左侧：Logo和标题 */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <div style={{ marginRight: 24 }}>
          <Text strong style={{ fontSize: 18, color: '#1890ff' }}>
            事业单位招聘推荐系统
          </Text>
        </div>
        
        {/* 主导航菜单 */}
        <Menu
          mode="horizontal"
          selectedKeys={[location.pathname]}
          items={getMenuItems()}
          onClick={handleMenuClick}
          style={{ border: 'none', backgroundColor: 'transparent' }}
        />
      </div>

      {/* 右侧：用户信息 */}
      <div>
        <Dropdown overlay={userMenu} placement="bottomRight">
          <Space style={{ cursor: 'pointer' }}>
            <Avatar icon={<UserOutlined />} />
            <Text>{user?.name}</Text>
          </Space>
        </Dropdown>
      </div>
    </Header>
  );
};

export default AppHeader;
