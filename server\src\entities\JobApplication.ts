import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { IsNumber, Min, Max } from 'class-validator';
import { Candidate } from './Candidate';
import { Job } from './Job';

export enum ApplicationStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  WITHDRAWN = 'withdrawn',
}

@Entity('job_applications')
export class JobApplication {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({
    type: 'enum',
    enum: ApplicationStatus,
    default: ApplicationStatus.PENDING,
  })
  status: ApplicationStatus;

  @Column({ type: 'text', nullable: true })
  coverLetter: string;

  // 推荐系统相关
  @Column({ type: 'decimal', precision: 5, scale: 4, nullable: true })
  @IsNumber()
  @Min(0)
  @Max(1)
  matchScore: number; // 匹配度分数

  @Column({ type: 'json', nullable: true })
  recommendationReason: {
    abilityMatch: number;
    educationMatch: number;
    experienceMatch: number;
    skillMatch: number;
    ethnicPreference: number;
    overallScore: number;
    strengths: string[];
    improvements: string[];
  };

  @Column({ default: false })
  isRecommended: boolean; // 是否为系统推荐

  @Column({ type: 'timestamp', nullable: true })
  appliedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  reviewedAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => Candidate, (candidate) => candidate.applications)
  @JoinColumn({ name: 'candidateId' })
  candidate: Candidate;

  @Column()
  candidateId: string;

  @ManyToOne(() => Job, (job) => job.applications)
  @JoinColumn({ name: 'jobId' })
  job: Job;

  @Column()
  jobId: string;
}
