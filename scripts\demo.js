#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🎯 事业单位招聘报考推荐系统演示\n');

// 检查项目是否已设置
if (!fs.existsSync('client/.env') || !fs.existsSync('server/.env')) {
  console.log('⚠️  项目尚未设置，正在运行初始化...\n');
  try {
    execSync('npm run setup', { stdio: 'inherit' });
  } catch (error) {
    console.error('❌ 初始化失败');
    process.exit(1);
  }
}

console.log('📋 系统功能演示清单:\n');

const features = [
  {
    title: '🔐 用户认证系统',
    description: '支持用户注册、登录、权限管理',
    status: '✅ 已完成',
    demo: [
      '• 管理员账号: <EMAIL> / admin123',
      '• 支持JWT token认证',
      '• 角色权限控制 (管理员/HR/考生)'
    ]
  },
  {
    title: '👤 考生信息管理',
    description: '考生个人资料、教育背景、能力评估',
    status: '🔄 开发中',
    demo: [
      '• 个人基本信息录入',
      '• 教育背景管理',
      '• 多维度能力评估',
      '• 考试记录管理'
    ]
  },
  {
    title: '💼 岗位信息系统',
    description: '岗位发布、管理、搜索功能',
    status: '🔄 开发中',
    demo: [
      '• 岗位信息展示',
      '• 多条件筛选搜索',
      '• 岗位详情查看',
      '• 报名状态跟踪'
    ]
  },
  {
    title: '🎯 智能推荐算法',
    description: '基于能力匹配的岗位推荐',
    status: '🔄 开发中',
    demo: [
      '• 混合推荐模型',
      '• 能力-岗位匹配分析',
      '• 竞争度评估',
      '• 推荐理由解释'
    ]
  },
  {
    title: '📊 数据分析模块',
    description: '报考趋势、竞争分析、能力画像',
    status: '🔄 开发中',
    demo: [
      '• 个人能力雷达图',
      '• 报考趋势分析',
      '• 岗位竞争热度',
      '• 成功率预测'
    ]
  },
  {
    title: '📈 可视化图表',
    description: '数据可视化展示',
    status: '🔄 开发中',
    demo: [
      '• ECharts图表集成',
      '• 实时数据更新',
      '• 交互式图表',
      '• 导出功能'
    ]
  }
];

features.forEach((feature, index) => {
  console.log(`${index + 1}. ${feature.title}`);
  console.log(`   ${feature.description}`);
  console.log(`   状态: ${feature.status}\n`);
  
  feature.demo.forEach(item => {
    console.log(`   ${item}`);
  });
  console.log('');
});

console.log('🏗️  技术架构:\n');

const techStack = {
  '前端': [
    'React 18 + TypeScript',
    'Ant Design UI组件库',
    'React Router 路由管理',
    'Axios HTTP客户端',
    'ECharts 数据可视化'
  ],
  '后端': [
    'Node.js + Express',
    'TypeORM 数据库ORM',
    'JWT 身份认证',
    'bcryptjs 密码加密',
    'PostgreSQL 数据库'
  ],
  '推荐算法': [
    '协同过滤算法',
    '内容推荐算法',
    '知识推荐算法',
    '混合推荐模型',
    '项目反应理论(IRT)'
  ]
};

Object.entries(techStack).forEach(([category, technologies]) => {
  console.log(`📦 ${category}:`);
  technologies.forEach(tech => {
    console.log(`   • ${tech}`);
  });
  console.log('');
});

console.log('🎮 快速体验:\n');
console.log('1. 确保PostgreSQL已启动');
console.log('2. 创建数据库: createdb recruitment_recommendation');
console.log('3. 初始化数据: cd server && npm run init-db');
console.log('4. 启动服务: npm run dev');
console.log('5. 访问: http://localhost:3000');
console.log('6. 登录: <EMAIL> / admin123\n');

console.log('📚 更多信息:');
console.log('• 快速启动: 查看 QUICKSTART.md');
console.log('• API文档: 查看 docs/API.md');
console.log('• 部署指南: 查看 docs/DEPLOYMENT.md');
console.log('• 开题报告: 查看 开题报告.txt\n');

console.log('🎉 感谢使用事业单位招聘报考推荐系统！');
