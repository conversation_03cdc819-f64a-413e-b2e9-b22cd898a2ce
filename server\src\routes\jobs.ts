import { Router } from 'express';
import { JobController } from '../controllers/JobController';
import { authenticateToken, requireHR } from '../middleware/auth';

const router = Router();
const jobController = new JobController();

// 公开路由（需要认证但不需要特殊权限）
router.use(authenticateToken);

router.get('/', jobController.getJobs);
router.get('/:id', jobController.getJobById);

// 需要HR权限的路由
router.post('/', requireHR, jobController.createJob);
router.put('/:id', requireHR, jobController.updateJob);
router.delete('/:id', requireHR, jobController.deleteJob);

// 岗位统计
router.get('/:id/statistics', jobController.getJobStatistics);
router.get('/:id/applications', requireHR, jobController.getJobApplications);

export default router;
