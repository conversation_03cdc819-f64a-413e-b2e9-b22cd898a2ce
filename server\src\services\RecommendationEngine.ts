import { AppDataSource } from '../config/database';
import { Job } from '../entities/Job';
import { Candidate } from '../entities/Candidate';
import { JobApplication } from '../entities/JobApplication';
import { ExamRecord } from '../entities/ExamRecord';
import { JobMatchingService, MatchResult } from './JobMatchingService';
import { AbilityAnalysisService } from './AbilityAnalysisService';

export interface RecommendationResult {
  job: Job;
  score: number;
  reason: string;
  confidence: number;
  tags: string[];
  competitionLevel: 'low' | 'medium' | 'high';
  successProbability: number;
  explanation: {
    strengths: string[];
    concerns: string[];
    suggestions: string[];
  };
}

export interface RecommendationConfig {
  maxResults: number;
  minScore: number;
  diversityWeight: number;
  noveltyWeight: number;
  popularityWeight: number;
}

export class RecommendationEngine {
  private jobRepository = AppDataSource.getRepository(Job);
  private candidateRepository = AppDataSource.getRepository(Candidate);
  private applicationRepository = AppDataSource.getRepository(JobApplication);
  private examRecordRepository = AppDataSource.getRepository(ExamRecord);
  
  private jobMatchingService = new JobMatchingService();
  private abilityService = new AbilityAnalysisService();

  /**
   * 混合推荐算法主入口
   */
  async generateRecommendations(
    candidateId: string, 
    config: Partial<RecommendationConfig> = {}
  ): Promise<RecommendationResult[]> {
    const defaultConfig: RecommendationConfig = {
      maxResults: 10,
      minScore: 0.3,
      diversityWeight: 0.2,
      noveltyWeight: 0.1,
      popularityWeight: 0.1,
    };

    const finalConfig = { ...defaultConfig, ...config };

    const candidate = await this.candidateRepository.findOne({
      where: { id: candidateId },
      relations: ['examRecords', 'applications'],
    });

    if (!candidate) {
      throw new Error('考生不存在');
    }

    // 1. 协同过滤推荐
    const collaborativeResults = await this.collaborativeFiltering(candidate);
    
    // 2. 内容推荐
    const contentResults = await this.contentBasedRecommendation(candidate);
    
    // 3. 知识推荐
    const knowledgeResults = await this.knowledgeBasedRecommendation(candidate);
    
    // 4. 混合算法融合
    const hybridResults = await this.hybridRecommendation(
      collaborativeResults,
      contentResults,
      knowledgeResults,
      candidate
    );

    // 5. 多样性和新颖性优化
    const optimizedResults = this.optimizeForDiversityAndNovelty(
      hybridResults,
      finalConfig
    );

    // 6. 最终排序和筛选
    return this.finalRanking(optimizedResults, finalConfig);
  }

  /**
   * 协同过滤推荐算法
   */
  private async collaborativeFiltering(candidate: Candidate): Promise<MatchResult[]> {
    // 找到相似的考生
    const similarCandidates = await this.findSimilarCandidates(candidate);
    
    // 获取相似考生申请的岗位
    const recommendedJobs = new Map<string, number>();
    
    for (const similarCandidate of similarCandidates) {
      const applications = await this.applicationRepository.find({
        where: { candidateId: similarCandidate.id },
        relations: ['job'],
      });

      applications.forEach(app => {
        if (app.job && app.job.isActive) {
          const currentScore = recommendedJobs.get(app.job.id) || 0;
          const similarity = this.calculateCandidateSimilarity(candidate, similarCandidate);
          recommendedJobs.set(app.job.id, currentScore + similarity);
        }
      });
    }

    // 转换为MatchResult格式
    const results: MatchResult[] = [];
    for (const [jobId, score] of recommendedJobs.entries()) {
      const job = await this.jobRepository.findOne({ where: { id: jobId } });
      if (job) {
        const matchResult = await this.jobMatchingService.calculateJobMatch(candidate, job);
        matchResult.matchScore = (matchResult.matchScore + score) / 2; // 融合协同过滤分数
        results.push(matchResult);
      }
    }

    return results.sort((a, b) => b.matchScore - a.matchScore);
  }

  /**
   * 基于内容的推荐算法
   */
  private async contentBasedRecommendation(candidate: Candidate): Promise<MatchResult[]> {
    const activeJobs = await this.jobRepository.find({
      where: { isActive: true },
    });

    const results: MatchResult[] = [];

    for (const job of activeJobs) {
      const matchResult = await this.jobMatchingService.calculateJobMatch(candidate, job);
      
      // 基于内容特征增强匹配分数
      const contentScore = this.calculateContentSimilarity(candidate, job);
      matchResult.matchScore = (matchResult.matchScore * 0.7 + contentScore * 0.3);
      
      results.push(matchResult);
    }

    return results.sort((a, b) => b.matchScore - a.matchScore);
  }

  /**
   * 基于知识的推荐算法
   */
  private async knowledgeBasedRecommendation(candidate: Candidate): Promise<MatchResult[]> {
    const rules = this.getRecommendationRules();
    const activeJobs = await this.jobRepository.find({
      where: { isActive: true },
    });

    const results: MatchResult[] = [];

    for (const job of activeJobs) {
      let knowledgeScore = 0;
      let ruleCount = 0;

      // 应用推荐规则
      for (const rule of rules) {
        if (rule.condition(candidate, job)) {
          knowledgeScore += rule.weight;
          ruleCount++;
        }
      }

      if (ruleCount > 0) {
        const matchResult = await this.jobMatchingService.calculateJobMatch(candidate, job);
        matchResult.matchScore = (matchResult.matchScore * 0.8 + (knowledgeScore / ruleCount) * 0.2);
        results.push(matchResult);
      }
    }

    return results.sort((a, b) => b.matchScore - a.matchScore);
  }

  /**
   * 混合推荐算法
   */
  private async hybridRecommendation(
    collaborative: MatchResult[],
    content: MatchResult[],
    knowledge: MatchResult[],
    candidate: Candidate
  ): Promise<RecommendationResult[]> {
    const jobScores = new Map<string, {
      scores: number[];
      job: Job;
      matchResult: MatchResult;
    }>();

    // 收集所有推荐结果
    const allResults = [...collaborative, ...content, ...knowledge];
    
    allResults.forEach(result => {
      const jobId = result.job.id;
      if (!jobScores.has(jobId)) {
        jobScores.set(jobId, {
          scores: [],
          job: result.job,
          matchResult: result,
        });
      }
      jobScores.get(jobId)!.scores.push(result.matchScore);
    });

    // 计算混合分数
    const recommendations: RecommendationResult[] = [];
    
    for (const [jobId, data] of jobScores.entries()) {
      const { scores, job, matchResult } = data;
      
      // 使用加权平均计算最终分数
      const weights = [0.4, 0.4, 0.2]; // 协同过滤、内容推荐、知识推荐的权重
      let finalScore = 0;
      let totalWeight = 0;

      scores.forEach((score, index) => {
        if (index < weights.length) {
          finalScore += score * weights[index];
          totalWeight += weights[index];
        }
      });

      finalScore = totalWeight > 0 ? finalScore / totalWeight : 0;

      // 生成推荐解释
      const explanation = await this.generateExplanation(candidate, job, matchResult);
      
      recommendations.push({
        job,
        score: finalScore,
        reason: this.generateRecommendationReason(matchResult),
        confidence: this.calculateConfidence(scores),
        tags: this.generateTags(candidate, job),
        competitionLevel: matchResult.competitionLevel,
        successProbability: matchResult.successProbability,
        explanation,
      });
    }

    return recommendations.sort((a, b) => b.score - a.score);
  }

  /**
   * 多样性和新颖性优化
   */
  private optimizeForDiversityAndNovelty(
    recommendations: RecommendationResult[],
    config: RecommendationConfig
  ): RecommendationResult[] {
    const optimized: RecommendationResult[] = [];
    const selectedTypes = new Set<string>();
    const selectedLocations = new Set<string>();

    for (const rec of recommendations) {
      let diversityBonus = 0;
      
      // 多样性奖励
      if (!selectedTypes.has(rec.job.type)) {
        diversityBonus += config.diversityWeight;
        selectedTypes.add(rec.job.type);
      }
      
      if (!selectedLocations.has(rec.job.location)) {
        diversityBonus += config.diversityWeight * 0.5;
        selectedLocations.add(rec.job.location);
      }

      // 新颖性奖励（新发布的岗位）
      const daysSinceCreated = (Date.now() - rec.job.createdAt.getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceCreated <= 7) {
        diversityBonus += config.noveltyWeight;
      }

      rec.score += diversityBonus;
      optimized.push(rec);
    }

    return optimized.sort((a, b) => b.score - a.score);
  }

  /**
   * 最终排序和筛选
   */
  private finalRanking(
    recommendations: RecommendationResult[],
    config: RecommendationConfig
  ): RecommendationResult[] {
    return recommendations
      .filter(rec => rec.score >= config.minScore)
      .slice(0, config.maxResults)
      .map(rec => ({
        ...rec,
        score: Math.round(rec.score * 100) / 100,
      }));
  }

  // 辅助方法
  private async findSimilarCandidates(candidate: Candidate): Promise<Candidate[]> {
    const allCandidates = await this.candidateRepository.find({
      relations: ['examRecords'],
    });

    const similarities = allCandidates
      .filter(c => c.id !== candidate.id)
      .map(c => ({
        candidate: c,
        similarity: this.calculateCandidateSimilarity(candidate, c),
      }))
      .sort((a, b) => b.similarity - a.similarity);

    return similarities.slice(0, 10).map(s => s.candidate);
  }

  private calculateCandidateSimilarity(candidate1: Candidate, candidate2: Candidate): number {
    let similarity = 0;
    let factors = 0;

    // 教育背景相似度
    if (candidate1.education === candidate2.education) {
      similarity += 0.3;
    }
    factors++;

    // 专业相似度
    if (candidate1.major === candidate2.major) {
      similarity += 0.4;
    }
    factors++;

    // 民族相似度
    if (candidate1.ethnicGroup === candidate2.ethnicGroup) {
      similarity += 0.1;
    }
    factors++;

    // 能力相似度
    const abilities1 = [
      candidate1.analyticalAbility || 60,
      candidate1.verbalAbility || 60,
      candidate1.numericalAbility || 60,
      candidate1.logicalAbility || 60,
      candidate1.comprehensiveAbility || 60,
    ];

    const abilities2 = [
      candidate2.analyticalAbility || 60,
      candidate2.verbalAbility || 60,
      candidate2.numericalAbility || 60,
      candidate2.logicalAbility || 60,
      candidate2.comprehensiveAbility || 60,
    ];

    const abilityDistance = Math.sqrt(
      abilities1.reduce((sum, a1, i) => sum + Math.pow(a1 - abilities2[i], 2), 0)
    );

    const maxDistance = Math.sqrt(5 * Math.pow(100, 2));
    const abilitySimilarity = 1 - (abilityDistance / maxDistance);
    similarity += abilitySimilarity * 0.2;
    factors++;

    return factors > 0 ? similarity / factors : 0;
  }

  private calculateContentSimilarity(candidate: Candidate, job: Job): number {
    let similarity = 0;

    // 教育匹配
    const educationLevels = { 'high_school': 1, 'college': 2, 'bachelor': 3, 'master': 4, 'doctor': 5 };
    const candidateLevel = educationLevels[candidate.education] || 0;
    const requiredLevel = educationLevels[job.minEducation] || 0;
    
    if (candidateLevel >= requiredLevel) {
      similarity += 0.3;
    }

    // 专业匹配
    if (job.preferredMajors && job.preferredMajors.includes(candidate.major)) {
      similarity += 0.4;
    }

    // 技能匹配
    if (candidate.skills && job.requiredSkills) {
      const candidateSkills = candidate.skills.toLowerCase().split(/[,，、\s]+/);
      const requiredSkills = job.requiredSkills.map(s => s.toLowerCase());
      
      const matchedSkills = requiredSkills.filter(skill => 
        candidateSkills.some(cs => cs.includes(skill) || skill.includes(cs))
      );
      
      similarity += (matchedSkills.length / requiredSkills.length) * 0.3;
    }

    return Math.min(similarity, 1);
  }

  private getRecommendationRules() {
    return [
      {
        name: '教育背景匹配',
        condition: (candidate: Candidate, job: Job) => {
          const levels = { 'high_school': 1, 'college': 2, 'bachelor': 3, 'master': 4, 'doctor': 5 };
          return (levels[candidate.education] || 0) >= (levels[job.minEducation] || 0);
        },
        weight: 0.8,
      },
      {
        name: '民族政策优惠',
        condition: (candidate: Candidate, job: Job) => {
          return job.ethnicPreference && 
                 job.preferredEthnicGroups?.includes(candidate.ethnicGroup);
        },
        weight: 0.9,
      },
      {
        name: '专业对口',
        condition: (candidate: Candidate, job: Job) => {
          return job.preferredMajors?.includes(candidate.major) || false;
        },
        weight: 0.85,
      },
      {
        name: '竞争度适中',
        condition: (candidate: Candidate, job: Job) => {
          // 这里需要实际的竞争度数据，暂时用简化逻辑
          return job.recruitmentCount >= 3;
        },
        weight: 0.6,
      },
    ];
  }

  private generateRecommendationReason(matchResult: MatchResult): string {
    const reasons = [];
    
    if (matchResult.recommendationReason.abilityMatch >= 0.8) {
      reasons.push('能力高度匹配');
    }
    if (matchResult.recommendationReason.educationMatch >= 0.8) {
      reasons.push('教育背景符合');
    }
    if (matchResult.recommendationReason.ethnicPreference > 0) {
      reasons.push('享受民族政策优惠');
    }
    if (matchResult.competitionLevel === 'low') {
      reasons.push('竞争压力较小');
    }

    return reasons.length > 0 ? reasons.join('，') : '综合匹配度较高';
  }

  private calculateConfidence(scores: number[]): number {
    if (scores.length === 0) return 0;
    
    const avg = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - avg, 2), 0) / scores.length;
    const stdDev = Math.sqrt(variance);
    
    // 标准差越小，置信度越高
    return Math.max(0, Math.min(1, 1 - stdDev));
  }

  private generateTags(candidate: Candidate, job: Job): string[] {
    const tags = [];
    
    if (job.ethnicPreference && job.preferredEthnicGroups?.includes(candidate.ethnicGroup)) {
      tags.push('民族优惠');
    }
    
    if (job.preferredMajors?.includes(candidate.major)) {
      tags.push('专业对口');
    }
    
    if (job.level === 'entry') {
      tags.push('入门级');
    }
    
    if (job.type === 'education') {
      tags.push('教育类');
    } else if (job.type === 'medical') {
      tags.push('医疗类');
    } else if (job.type === 'technical') {
      tags.push('技术类');
    }

    return tags;
  }

  private async generateExplanation(
    candidate: Candidate, 
    job: Job, 
    matchResult: MatchResult
  ): Promise<{ strengths: string[]; concerns: string[]; suggestions: string[] }> {
    const strengths = [];
    const concerns = [];
    const suggestions = [];

    // 分析优势
    if (matchResult.recommendationReason.abilityMatch >= 0.8) {
      strengths.push('您的能力水平与岗位要求高度匹配');
    }
    if (matchResult.recommendationReason.educationMatch >= 0.8) {
      strengths.push('您的教育背景完全符合岗位要求');
    }

    // 分析关注点
    if (matchResult.competitionLevel === 'high') {
      concerns.push('该岗位竞争较为激烈');
    }
    if (matchResult.recommendationReason.experienceMatch < 0.6) {
      concerns.push('工作经验可能略显不足');
    }

    // 生成建议
    if (matchResult.successProbability < 0.7) {
      suggestions.push('建议进一步提升相关能力');
    }
    suggestions.push('建议仔细阅读岗位要求，准备针对性材料');

    return { strengths, concerns, suggestions };
  }
}
