import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Typography, Tabs, Spin, message } from 'antd';
import { Bar<PERSON>hartOutlined, RadarChartOutlined, HeatMapOutlined } from '@ant-design/icons';
import AbilityRadarChart from '../../components/Charts/AbilityRadarChart';
import ApplicationTrendChart from '../../components/Charts/ApplicationTrendChart';
import JobHeatmapChart from '../../components/Charts/JobHeatmapChart';
import { apiService } from '../../services/api';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

interface AbilityData {
  analyticalAbility: number;
  verbalAbility: number;
  numericalAbility: number;
  logicalAbility: number;
  comprehensiveAbility: number;
  overallScore: number;
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
}

interface TrendData {
  period: string;
  applications: number;
  avgMatchScore: number;
}

interface HeatData {
  jobId: string;
  jobTitle: string;
  department: string;
  location: string;
  type: string;
  applications: number;
  recruitmentCount: number;
  competitionRatio: number;
  heatLevel: 'hot' | 'warm' | 'cold';
}

const Analytics: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [abilityData, setAbilityData] = useState<AbilityData | null>(null);
  const [trendData, setTrendData] = useState<TrendData[]>([]);
  const [heatData, setHeatData] = useState<HeatData[]>([]);
  const [activeTab, setActiveTab] = useState('ability');

  useEffect(() => {
    loadAnalyticsData();
  }, []);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);

      // 并行加载所有数据
      const [abilityResponse, trendResponse] = await Promise.all([
        apiService.get('/analytics/ability-profile'),
        apiService.get('/analytics/application-trends'),
      ]);

      if (abilityResponse.success) {
        setAbilityData(abilityResponse.data);
      }

      if (trendResponse.success) {
        setTrendData(trendResponse.data);
      }

      // 模拟热度数据
      setHeatData(generateMockHeatData());

    } catch (error: any) {
      console.error('加载分析数据失败:', error);
      message.error(error.response?.data?.error?.message || '加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  const generateMockHeatData = (): HeatData[] => {
    return [
      {
        jobId: '1',
        jobTitle: '黔南州教育局中学教师',
        department: '黔南州教育局',
        location: '都匀市',
        type: 'education',
        applications: 150,
        recruitmentCount: 5,
        competitionRatio: 30,
        heatLevel: 'hot',
      },
      {
        jobId: '2',
        jobTitle: '黔南州财政局会计',
        department: '黔南州财政局',
        location: '都匀市',
        type: 'administrative',
        applications: 45,
        recruitmentCount: 3,
        competitionRatio: 15,
        heatLevel: 'warm',
      },
      {
        jobId: '3',
        jobTitle: '黔南州人民医院护士',
        department: '黔南州人民医院',
        location: '都匀市',
        type: 'medical',
        applications: 80,
        recruitmentCount: 10,
        competitionRatio: 8,
        heatLevel: 'cold',
      },
      {
        jobId: '4',
        jobTitle: '黔南州科技局信息技术专员',
        department: '黔南州科技局',
        location: '都匀市',
        type: 'technical',
        applications: 24,
        recruitmentCount: 2,
        competitionRatio: 12,
        heatLevel: 'warm',
      },
    ];
  };

  const handleTrendPeriodChange = async (period: string) => {
    try {
      const response = await apiService.get(`/analytics/application-trends?period=${period}`);
      if (response.success) {
        setTrendData(response.data);
      }
    } catch (error) {
      console.error('更新趋势数据失败:', error);
    }
  };

  return (
    <div className="fade-in">
      <div style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          数据分析中心
        </Title>
        <Text type="secondary">
          基于大数据的智能分析，助您科学决策
        </Text>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        size="large"
        style={{ marginBottom: 24 }}
      >
        <TabPane
          tab={
            <span>
              <RadarChartOutlined />
              能力分析
            </span>
          }
          key="ability"
        >
          <Row gutter={[24, 24]}>
            <Col span={24}>
              <AbilityRadarChart
                data={abilityData}
                loading={loading}
                height={450}
              />
            </Col>
          </Row>
        </TabPane>

        <TabPane
          tab={
            <span>
              <BarChartOutlined />
              趋势分析
            </span>
          }
          key="trend"
        >
          <Row gutter={[24, 24]}>
            <Col span={24}>
              <ApplicationTrendChart
                data={trendData}
                loading={loading}
                height={350}
                onPeriodChange={handleTrendPeriodChange}
              />
            </Col>
          </Row>
        </TabPane>

        <TabPane
          tab={
            <span>
              <HeatMapOutlined />
              竞争热度
            </span>
          }
          key="heat"
        >
          <Row gutter={[24, 24]}>
            <Col span={24}>
              <JobHeatmapChart
                data={heatData}
                loading={loading}
                height={450}
              />
            </Col>
          </Row>
        </TabPane>
      </Tabs>

      {loading && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(255, 255, 255, 0.8)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <Spin size="large" />
        </div>
      )}
    </div>
  );
};

export default Analytics;
