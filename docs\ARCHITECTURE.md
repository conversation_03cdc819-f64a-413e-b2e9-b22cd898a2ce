# 系统架构文档

## 整体架构

事业单位招聘报考推荐系统采用前后端分离的架构设计，主要包含以下几个层次：

### 1. 表现层 (Presentation Layer)
- **前端框架**: React 18 + TypeScript
- **UI组件库**: Ant Design
- **状态管理**: React Context + useReducer
- **路由管理**: React Router
- **HTTP客户端**: Axios

### 2. 应用层 (Application Layer)
- **后端框架**: Node.js + Express
- **API设计**: RESTful API
- **认证授权**: JWT Token
- **中间件**: 认证、错误处理、日志记录

### 3. 业务逻辑层 (Business Logic Layer)
- **用户管理**: 注册、登录、权限控制
- **考生管理**: 个人信息、能力评估、考试记录
- **岗位管理**: 岗位发布、搜索、统计
- **推荐算法**: 混合推荐模型、匹配分析
- **数据分析**: 趋势分析、可视化

### 4. 数据访问层 (Data Access Layer)
- **ORM框架**: TypeORM
- **数据库**: PostgreSQL
- **缓存**: Redis (可选)
- **文件存储**: 本地文件系统

## 数据库设计

### 核心实体关系

```mermaid
erDiagram
    User ||--o{ Candidate : has
    User {
        uuid id PK
        string email UK
        string password
        string name
        enum role
        boolean isActive
        datetime createdAt
        datetime updatedAt
    }
    
    Candidate ||--o{ ExamRecord : has
    Candidate ||--o{ JobApplication : creates
    Candidate {
        uuid id PK
        string name
        string idCard
        date birthDate
        enum education
        string major
        enum ethnicGroup
        decimal analyticalAbility
        decimal verbalAbility
        decimal numericalAbility
        decimal logicalAbility
        decimal comprehensiveAbility
        uuid userId FK
    }
    
    Job ||--o{ JobApplication : receives
    Job {
        uuid id PK
        string title
        string department
        string location
        enum type
        enum level
        text description
        text requirements
        integer recruitmentCount
        enum minEducation
        array preferredMajors
        array requiredSkills
        boolean ethnicPreference
        decimal minSalary
        decimal maxSalary
        datetime registrationStartDate
        datetime registrationEndDate
        datetime examDate
    }
    
    ExamRecord {
        uuid id PK
        integer examYear
        enum examType
        string examName
        decimal publicBasicScore
        decimal professionalScore
        decimal writingScore
        decimal interviewScore
        decimal totalScore
        decimal ranking
        json questionAnalysis
        date examDate
        uuid candidateId FK
    }
    
    JobApplication {
        uuid id PK
        enum status
        text coverLetter
        decimal matchScore
        json recommendationReason
        boolean isRecommended
        datetime appliedAt
        uuid candidateId FK
        uuid jobId FK
    }
```

## 推荐算法架构

### 混合推荐模型

```mermaid
graph TD
    A[用户输入] --> B[数据预处理]
    B --> C[特征提取]
    C --> D[协同过滤]
    C --> E[内容推荐]
    C --> F[知识推荐]
    D --> G[混合算法]
    E --> G
    F --> G
    G --> H[排序算法]
    H --> I[推荐结果]
    I --> J[解释生成]
    J --> K[用户反馈]
    K --> L[模型优化]
    L --> B
```

### 能力评估模型

基于项目反应理论(IRT)的能力评估：

1. **数据收集**: 考试成绩、答题时间、题目难度
2. **能力建模**: 分析能力、语言能力、数值能力、逻辑能力、综合能力
3. **参数估计**: 使用最大似然估计法
4. **能力评分**: 生成0-100分的能力分数

### 匹配算法

```mermaid
graph LR
    A[考生能力向量] --> C[相似度计算]
    B[岗位要求向量] --> C
    C --> D[匹配度分数]
    D --> E[竞争度分析]
    E --> F[成功率预测]
    F --> G[推荐排序]
```

## 系统流程

### 用户注册流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant D as 数据库
    
    U->>F: 填写注册信息
    F->>B: POST /api/auth/register
    B->>B: 验证数据格式
    B->>D: 检查邮箱是否存在
    D-->>B: 返回查询结果
    B->>B: 加密密码
    B->>D: 保存用户信息
    D-->>B: 返回用户ID
    B->>B: 生成JWT Token
    B-->>F: 返回用户信息和Token
    F-->>U: 注册成功，自动登录
```

### 推荐生成流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant A as 推荐算法
    participant D as 数据库
    
    U->>F: 请求推荐
    F->>B: GET /api/recommendations
    B->>D: 获取用户能力数据
    D-->>B: 返回能力评估
    B->>D: 获取活跃岗位
    D-->>B: 返回岗位列表
    B->>A: 调用推荐算法
    A->>A: 计算匹配度
    A->>A: 生成推荐理由
    A-->>B: 返回推荐结果
    B-->>F: 返回推荐岗位
    F-->>U: 展示推荐结果
```

## 技术选型理由

### 前端技术栈
- **React**: 组件化开发，生态丰富，学习成本适中
- **TypeScript**: 类型安全，提高代码质量和开发效率
- **Ant Design**: 企业级UI组件库，设计规范统一

### 后端技术栈
- **Node.js**: JavaScript全栈开发，性能优秀
- **Express**: 轻量级Web框架，中间件丰富
- **TypeORM**: 类型安全的ORM，支持多种数据库

### 数据库选择
- **PostgreSQL**: 功能强大的关系型数据库，支持JSON数据类型，适合复杂查询

## 性能优化策略

### 前端优化
1. **代码分割**: 使用React.lazy实现路由级别的代码分割
2. **缓存策略**: 合理使用浏览器缓存和HTTP缓存
3. **图片优化**: 使用WebP格式，实现懒加载

### 后端优化
1. **数据库优化**: 合理设计索引，优化查询语句
2. **缓存机制**: 使用Redis缓存热点数据
3. **连接池**: 使用数据库连接池提高并发性能

### 推荐算法优化
1. **预计算**: 离线计算用户相似度矩阵
2. **增量更新**: 实现增量式模型更新
3. **并行计算**: 使用多线程并行计算推荐结果

## 安全设计

### 认证安全
- JWT Token过期机制
- 密码加密存储(bcrypt)
- 防止暴力破解

### 数据安全
- SQL注入防护
- XSS攻击防护
- CSRF攻击防护
- 敏感数据加密

### 接口安全
- 请求频率限制
- 参数验证
- 权限控制
- 日志审计

## 扩展性设计

### 水平扩展
- 无状态服务设计
- 负载均衡支持
- 数据库读写分离

### 功能扩展
- 插件化架构
- 微服务拆分准备
- API版本控制

### 数据扩展
- 分库分表策略
- 数据归档机制
- 备份恢复方案
