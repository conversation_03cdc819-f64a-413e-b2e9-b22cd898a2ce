import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import { Card, Typography, Spin } from 'antd';

const { Title, Text } = Typography;

interface AbilityData {
  analyticalAbility: number;
  verbalAbility: number;
  numericalAbility: number;
  logicalAbility: number;
  comprehensiveAbility: number;
  overallScore: number;
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
}

interface AbilityRadarChartProps {
  data: AbilityData | null;
  loading?: boolean;
  height?: number;
}

const AbilityRadarChart: React.FC<AbilityRadarChartProps> = ({
  data,
  loading = false,
  height = 400,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (!chartRef.current || loading || !data) return;

    // 初始化图表
    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
    }

    const chart = chartInstance.current;

    // 配置雷达图数据
    const radarData = [
      { name: '分析能力', max: 100 },
      { name: '语言能力', max: 100 },
      { name: '数值能力', max: 100 },
      { name: '逻辑能力', max: 100 },
      { name: '综合能力', max: 100 },
    ];

    const seriesData = [
      data.analyticalAbility,
      data.verbalAbility,
      data.numericalAbility,
      data.logicalAbility,
      data.comprehensiveAbility,
    ];

    // 计算平均水平作为对比
    const averageData = [60, 60, 60, 60, 60];

    const option: echarts.EChartsOption = {
      title: {
        text: '能力画像分析',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          if (params.seriesType === 'radar') {
            const dataIndex = params.dataIndex;
            const abilityNames = ['分析能力', '语言能力', '数值能力', '逻辑能力', '综合能力'];
            const score = seriesData[dataIndex];
            return `${abilityNames[dataIndex]}: ${score}分`;
          }
          return '';
        },
      },
      legend: {
        data: ['您的能力', '平均水平'],
        bottom: 10,
      },
      radar: {
        indicator: radarData,
        center: ['50%', '55%'],
        radius: '65%',
        axisName: {
          color: '#666',
          fontSize: 12,
        },
        splitLine: {
          lineStyle: {
            color: '#e8e8e8',
          },
        },
        axisLine: {
          lineStyle: {
            color: '#d9d9d9',
          },
        },
        splitArea: {
          show: true,
          areaStyle: {
            color: ['rgba(114, 172, 209, 0.05)', 'rgba(114, 172, 209, 0.1)'],
          },
        },
      },
      series: [
        {
          name: '能力对比',
          type: 'radar',
          data: [
            {
              value: seriesData,
              name: '您的能力',
              itemStyle: {
                color: '#1890ff',
              },
              areaStyle: {
                color: 'rgba(24, 144, 255, 0.2)',
              },
              lineStyle: {
                width: 2,
              },
            },
            {
              value: averageData,
              name: '平均水平',
              itemStyle: {
                color: '#52c41a',
              },
              areaStyle: {
                color: 'rgba(82, 196, 26, 0.1)',
              },
              lineStyle: {
                width: 1,
                type: 'dashed',
              },
            },
          ],
        },
      ],
    };

    chart.setOption(option);

    // 响应式处理
    const handleResize = () => {
      chart.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [data, loading, height]);

  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, []);

  const getScoreColor = (score: number) => {
    if (score >= 80) return '#52c41a';
    if (score >= 70) return '#faad14';
    if (score >= 60) return '#fa8c16';
    return '#f5222d';
  };

  const getScoreLevel = (score: number) => {
    if (score >= 80) return '优秀';
    if (score >= 70) return '良好';
    if (score >= 60) return '一般';
    return '需提升';
  };

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text type="secondary">正在分析能力数据...</Text>
          </div>
        </div>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Text type="secondary">暂无能力数据</Text>
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <div style={{ marginBottom: 16 }}>
        <Title level={4} style={{ margin: 0 }}>
          能力画像分析
        </Title>
        <Text type="secondary">
          基于考试记录分析的多维度能力评估
        </Text>
      </div>

      <div ref={chartRef} style={{ height: `${height}px`, width: '100%' }} />

      <div style={{ marginTop: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: 24, fontWeight: 'bold', color: getScoreColor(data.overallScore) }}>
              {data.overallScore}
            </div>
            <div style={{ color: '#666', fontSize: 12 }}>综合评分</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: 16, fontWeight: 'bold', color: getScoreColor(data.overallScore) }}>
              {getScoreLevel(data.overallScore)}
            </div>
            <div style={{ color: '#666', fontSize: 12 }}>能力等级</div>
          </div>
        </div>

        {data.strengths.length > 0 && (
          <div style={{ marginBottom: 12 }}>
            <Text strong style={{ color: '#52c41a' }}>优势能力：</Text>
            <Text>{data.strengths.join('、')}</Text>
          </div>
        )}

        {data.weaknesses.length > 0 && (
          <div style={{ marginBottom: 12 }}>
            <Text strong style={{ color: '#fa8c16' }}>待提升：</Text>
            <Text>{data.weaknesses.join('、')}</Text>
          </div>
        )}

        {data.recommendations.length > 0 && (
          <div>
            <Text strong style={{ color: '#1890ff' }}>建议：</Text>
            <ul style={{ margin: '8px 0', paddingLeft: 20 }}>
              {data.recommendations.map((rec, index) => (
                <li key={index} style={{ marginBottom: 4 }}>
                  <Text>{rec}</Text>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </Card>
  );
};

export default AbilityRadarChart;
