# 部署指南

## 环境要求

### 开发环境
- Node.js 18+
- PostgreSQL 13+
- npm 或 yarn

### 生产环境
- Node.js 18+
- PostgreSQL 13+
- Nginx (可选)
- PM2 (可选)

## 本地开发部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd recruitment-recommendation-system
```

### 2. 安装依赖
```bash
npm run install:all
```

### 3. 配置环境变量

#### 后端配置
复制 `server/.env.example` 到 `server/.env` 并配置：
```env
PORT=5000
NODE_ENV=development
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_password
DB_DATABASE=recruitment_recommendation
JWT_SECRET=your_super_secret_jwt_key
```

#### 前端配置
复制 `client/.env.example` 到 `client/.env` 并配置：
```env
VITE_API_URL=http://localhost:5000/api
```

### 4. 数据库设置
```bash
# 创建数据库
createdb recruitment_recommendation

# 运行迁移（如果有）
cd server
npm run migration:run
```

### 5. 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:3000

## 生产环境部署

### 1. 构建项目
```bash
npm run build
```

### 2. 使用 PM2 部署
```bash
# 安装 PM2
npm install -g pm2

# 启动后端
cd server
pm2 start dist/index.js --name "recruitment-api"

# 配置 Nginx 代理前端静态文件
```

### 3. Nginx 配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /path/to/client/dist;
        try_files $uri $uri/ /index.html;
    }

    # API 代理
    location /api {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 4. 数据库迁移
```bash
cd server
npm run migration:run
```

### 5. 启动服务
```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

## Docker 部署

### 1. 创建 Dockerfile
```dockerfile
# 多阶段构建
FROM node:18-alpine AS builder

# 构建前端
WORKDIR /app/client
COPY client/package*.json ./
RUN npm ci
COPY client/ ./
RUN npm run build

# 构建后端
WORKDIR /app/server
COPY server/package*.json ./
RUN npm ci
COPY server/ ./
RUN npm run build

# 生产镜像
FROM node:18-alpine
WORKDIR /app

# 复制后端构建结果
COPY --from=builder /app/server/dist ./dist
COPY --from=builder /app/server/package*.json ./
RUN npm ci --only=production

# 复制前端构建结果
COPY --from=builder /app/client/dist ./public

EXPOSE 5000
CMD ["node", "dist/index.js"]
```

### 2. Docker Compose
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - DB_HOST=db
    depends_on:
      - db

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=recruitment_recommendation
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

## 监控和日志

### 1. PM2 监控
```bash
pm2 monit
pm2 logs
```

### 2. 日志配置
在生产环境中配置适当的日志级别和轮转。

## 备份策略

### 1. 数据库备份
```bash
# 每日备份
pg_dump recruitment_recommendation > backup_$(date +%Y%m%d).sql
```

### 2. 文件备份
备份上传的文件和配置文件。

## 安全考虑

1. 使用 HTTPS
2. 配置防火墙
3. 定期更新依赖
4. 使用强密码和密钥
5. 限制数据库访问
6. 配置 CORS 策略
