import React from 'react';
import { Row, Col, Card, Typography, Statistic, Space, Button } from 'antd';
import {
  BankOutlined,
  UserOutlined,
  StarOutlined,
  TrophyOutlined,
  RightOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  // 模拟统计数据
  const stats = {
    totalJobs: 156,
    activeJobs: 89,
    myApplications: 12,
    recommendedJobs: 8,
  };

  const quickActions = [
    {
      title: '浏览岗位',
      description: '查看最新的招聘岗位信息',
      icon: <BankOutlined style={{ fontSize: 24, color: '#1890ff' }} />,
      action: () => navigate('/jobs'),
    },
    {
      title: '智能推荐',
      description: '获取个性化的岗位推荐',
      icon: <StarOutlined style={{ fontSize: 24, color: '#52c41a' }} />,
      action: () => navigate('/recommendations'),
    },
    {
      title: '完善资料',
      description: '完善个人信息以获得更准确的推荐',
      icon: <UserOutlined style={{ fontSize: 24, color: '#faad14' }} />,
      action: () => navigate('/profile'),
    },
    {
      title: '数据分析',
      description: '查看报考趋势和竞争分析',
      icon: <TrophyOutlined style={{ fontSize: 24, color: '#f5222d' }} />,
      action: () => navigate('/analytics'),
    },
  ];

  return (
    <div className="fade-in">
      {/* 欢迎信息 */}
      <Card style={{ marginBottom: 24 }}>
        <Space direction="vertical" size="small">
          <Title level={3} style={{ margin: 0 }}>
            欢迎回来，{user?.name}！
          </Title>
          <Text type="secondary">
            今天是 {new Date().toLocaleDateString('zh-CN', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              weekday: 'long'
            })}
          </Text>
        </Space>
      </Card>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总岗位数"
              value={stats.totalJobs}
              prefix={<BankOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="活跃岗位"
              value={stats.activeJobs}
              prefix={<StarOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="我的申请"
              value={stats.myApplications}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="推荐岗位"
              value={stats.recommendedJobs}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card title="快速操作" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]}>
          {quickActions.map((action, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <Card
                hoverable
                onClick={action.action}
                style={{ height: '100%', cursor: 'pointer' }}
                bodyStyle={{ padding: 20 }}
              >
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center' }}>
                    {action.icon}
                  </div>
                  <div style={{ textAlign: 'center' }}>
                    <Title level={5} style={{ margin: 0 }}>
                      {action.title}
                    </Title>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {action.description}
                    </Text>
                  </div>
                  <div style={{ textAlign: 'center' }}>
                    <Button type="link" icon={<RightOutlined />}>
                      立即前往
                    </Button>
                  </div>
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 最近活动 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="最新岗位" extra={<a href="#" onClick={() => navigate('/jobs')}>查看更多</a>}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {[1, 2, 3].map((item) => (
                <div key={item} style={{ padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div>
                      <Text strong>黔南州教育局教师岗位</Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        黔南州 · 教育类 · 本科及以上
                      </Text>
                    </div>
                    <Button size="small" type="primary">
                      查看
                    </Button>
                  </div>
                </div>
              ))}
            </Space>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="推荐岗位" extra={<a href="#" onClick={() => navigate('/recommendations')}>查看更多</a>}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {[1, 2, 3].map((item) => (
                <div key={item} style={{ padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div>
                      <Text strong>黔南州财政局会计岗位</Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        匹配度: 85% · 竞争度: 中等
                      </Text>
                    </div>
                    <Button size="small" type="primary">
                      申请
                    </Button>
                  </div>
                </div>
              ))}
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
