# API 文档

## 认证相关 API

### POST /api/auth/register
用户注册

**请求体:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "张三",
  "phone": "13800138000"
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "name": "张三",
      "role": "candidate"
    },
    "token": "jwt_token"
  },
  "message": "注册成功"
}
```

### POST /api/auth/login
用户登录

**请求体:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### GET /api/auth/me
获取当前用户信息

**Headers:**
```
Authorization: Bearer <token>
```

## 岗位相关 API

### GET /api/jobs
获取岗位列表

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `type`: 岗位类型
- `location`: 地区
- `keyword`: 关键词搜索

### GET /api/jobs/:id
获取岗位详情

### POST /api/jobs
创建岗位 (需要HR权限)

## 推荐相关 API

### GET /api/recommendations
获取推荐岗位

### GET /api/recommendations/refresh
刷新推荐

### POST /api/recommendations/:jobId/feedback
提交推荐反馈

## 分析相关 API

### GET /api/analytics/dashboard
获取仪表板统计数据

### GET /api/analytics/ability-profile
获取能力画像

### GET /api/analytics/application-trends
获取申请趋势

## 考生相关 API

### GET /api/candidates/profile
获取考生资料

### POST /api/candidates/profile
创建考生资料

### PUT /api/candidates/profile
更新考生资料

### GET /api/candidates/exam-records
获取考试记录

### POST /api/candidates/exam-records
添加考试记录

## 错误响应格式

```json
{
  "success": false,
  "error": {
    "message": "错误信息",
    "details": "详细错误信息"
  }
}
```

## 状态码

- 200: 成功
- 201: 创建成功
- 400: 请求错误
- 401: 未认证
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器错误
