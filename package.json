{"name": "recruitment-recommendation-system", "version": "1.0.0", "description": "事业单位招聘报考推荐系统", "main": "index.js", "scripts": {"setup": "node scripts/setup.js", "demo": "node scripts/demo.js", "dev": "concurrently \"npm run dev:client\" \"npm run dev:server\"", "dev:client": "cd client && npm run dev", "dev:server": "cd server && npm run dev", "build": "npm run build:client && npm run build:server", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "start": "cd server && npm start", "install:all": "npm install && cd client && npm install && cd ../server && npm install", "init-db": "cd server && npm run init-db", "test": "cd server && npm test", "lint": "cd client && npm run lint && cd ../server && npm run lint"}, "keywords": ["recruitment", "recommendation", "job-matching", "data-analysis"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}