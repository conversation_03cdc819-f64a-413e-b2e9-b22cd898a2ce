import React, { useEffect } from 'react';
import { Form, Input, Button, Card, Typography, Alert, Space, Divider } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';

const { Title, Text } = Typography;

interface LoginFormData {
  email: string;
  password: string;
}

const Login: React.FC = () => {
  const { login, loading, error, clearError } = useAuth();
  const [form] = Form.useForm();

  useEffect(() => {
    // 清除之前的错误信息
    clearError();
  }, [clearError]);

  const handleSubmit = async (values: LoginFormData) => {
    try {
      await login(values.email, values.password);
    } catch (error) {
      // 错误已经在AuthContext中处理
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px'
    }}>
      <Card
        style={{
          width: '100%',
          maxWidth: 400,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          borderRadius: 12
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <Title level={2} style={{ color: '#1890ff', marginBottom: 8 }}>
            欢迎登录
          </Title>
          <Text type="secondary">
            事业单位招聘报考推荐系统
          </Text>
        </div>

        {error && (
          <Alert
            message={error}
            type="error"
            showIcon
            closable
            onClose={clearError}
            style={{ marginBottom: 24 }}
          />
        )}

        <Form
          form={form}
          name="login"
          onFinish={handleSubmit}
          layout="vertical"
          size="large"
        >
          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="请输入邮箱"
              autoComplete="email"
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="密码"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6位字符' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入密码"
              autoComplete="current-password"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              style={{ height: 48 }}
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <Divider>
          <Text type="secondary">其他操作</Text>
        </Divider>

        <Space direction="vertical" style={{ width: '100%', textAlign: 'center' }}>
          <Text type="secondary">
            还没有账号？{' '}
            <Link to="/register" style={{ color: '#1890ff' }}>
              立即注册
            </Link>
          </Text>
          <Text type="secondary">
            <a href="#" style={{ color: '#1890ff' }}>
              忘记密码？
            </a>
          </Text>
        </Space>

        <div style={{ marginTop: 32, textAlign: 'center' }}>
          <Text type="secondary" style={{ fontSize: 12 }}>
            登录即表示您同意我们的服务条款和隐私政策
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default Login;
