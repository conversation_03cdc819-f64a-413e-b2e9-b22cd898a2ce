import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { IsNotEmpty, IsOptional, IsNumber, Min, Max } from 'class-validator';
import { User } from './User';
import { ExamRecord } from './ExamRecord';
import { JobApplication } from './JobApplication';

export enum Education {
  HIGH_SCHOOL = 'high_school',
  COLLEGE = 'college',
  BACHELOR = 'bachelor',
  MASTER = 'master',
  DOCTOR = 'doctor',
}

export enum EthnicGroup {
  HAN = 'han',
  MIAO = 'miao',
  BUYI = 'buyi',
  DONG = 'dong',
  TU = 'tu',
  YI = 'yi',
  GELAO = 'gelao',
  SHUI = 'shui',
  HUI = 'hui',
  ZHUANG = 'zhuang',
  YAO = 'yao',
  BAI = 'bai',
  OTHER = 'other',
}

@Entity('candidates')
export class Candidate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @IsNotEmpty()
  name: string;

  @Column()
  @IsNotEmpty()
  idCard: string;

  @Column({ type: 'date' })
  birthDate: Date;

  @Column({
    type: 'enum',
    enum: Education,
  })
  education: Education;

  @Column()
  @IsNotEmpty()
  major: string;

  @Column()
  @IsNotEmpty()
  graduateSchool: string;

  @Column({ type: 'date' })
  graduateDate: Date;

  @Column({
    type: 'enum',
    enum: EthnicGroup,
    default: EthnicGroup.HAN,
  })
  ethnicGroup: EthnicGroup;

  @Column({ nullable: true })
  @IsOptional()
  workExperience: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  skills: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  certifications: string;

  // 能力评估分数
  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  analyticalAbility: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  verbalAbility: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  numericalAbility: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  logicalAbility: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  comprehensiveAbility: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, (user) => user.candidates)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  userId: string;

  @OneToMany(() => ExamRecord, (examRecord) => examRecord.candidate)
  examRecords: ExamRecord[];

  @OneToMany(() => JobApplication, (application) => application.candidate)
  applications: JobApplication[];
}
