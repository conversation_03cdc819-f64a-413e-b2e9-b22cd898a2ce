import { Router } from 'express';
import { RecommendationController } from '../controllers/RecommendationController';
import { authenticateToken } from '../middleware/auth';

const router = Router();
const recommendationController = new RecommendationController();

// 所有路由都需要认证
router.use(authenticateToken);

// 获取推荐岗位
router.get('/', recommendationController.getRecommendations);
router.get('/refresh', recommendationController.refreshRecommendations);

// 推荐反馈
router.post('/:jobId/feedback', recommendationController.submitFeedback);

// 推荐解释
router.get('/:jobId/explanation', recommendationController.getRecommendationExplanation);

export default router;
