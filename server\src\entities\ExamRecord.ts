import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { IsNumber, Min, Max } from 'class-validator';
import { Candidate } from './Candidate';

export enum ExamType {
  WRITTEN = 'written',
  INTERVIEW = 'interview',
  PRACTICAL = 'practical',
  COMPREHENSIVE = 'comprehensive',
}

@Entity('exam_records')
export class ExamRecord {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  examYear: number;

  @Column({
    type: 'enum',
    enum: ExamType,
  })
  examType: ExamType;

  @Column()
  examName: string;

  // 各科目分数
  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  @IsNumber()
  @Min(0)
  @Max(100)
  publicBasicScore: number; // 公共基础知识

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  @IsNumber()
  @Min(0)
  @Max(100)
  professionalScore: number; // 专业知识

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  @IsNumber()
  @Min(0)
  @Max(100)
  writingScore: number; // 写作

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  @IsNumber()
  @Min(0)
  @Max(100)
  interviewScore: number; // 面试

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  @IsNumber()
  @Min(0)
  @Max(100)
  totalScore: number; // 总分

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  @IsNumber()
  @Min(0)
  @Max(100)
  ranking: number; // 排名

  // 题目分析数据（用于能力评估）
  @Column({ type: 'json', nullable: true })
  questionAnalysis: {
    correctCount: number;
    totalCount: number;
    timeSpent: number;
    difficultyDistribution: {
      easy: number;
      medium: number;
      hard: number;
    };
    categoryPerformance: {
      [category: string]: {
        correct: number;
        total: number;
      };
    };
  };

  @Column({ type: 'date' })
  examDate: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => Candidate, (candidate) => candidate.examRecords)
  @JoinColumn({ name: 'candidateId' })
  candidate: Candidate;

  @Column()
  candidateId: string;
}
