import { AppDataSource } from '../config/database';
import { Job } from '../entities/Job';
import { Candidate } from '../entities/Candidate';
import { JobApplication } from '../entities/JobApplication';
import { AbilityAnalysisService } from './AbilityAnalysisService';

export interface MatchResult {
  job: Job;
  matchScore: number;
  competitionLevel: 'low' | 'medium' | 'high';
  successProbability: number;
  recommendationReason: {
    abilityMatch: number;
    educationMatch: number;
    experienceMatch: number;
    skillMatch: number;
    ethnicPreference: number;
    overallScore: number;
    strengths: string[];
    improvements: string[];
  };
}

export interface CompetitionAnalysis {
  totalApplications: number;
  competitionRatio: number;
  averageScore: number;
  competitionLevel: 'low' | 'medium' | 'high';
  similarCandidates: number;
}

export class JobMatchingService {
  private jobRepository = AppDataSource.getRepository(Job);
  private candidateRepository = AppDataSource.getRepository(Candidate);
  private applicationRepository = AppDataSource.getRepository(JobApplication);
  private abilityService = new AbilityAnalysisService();

  /**
   * 为考生匹配合适的岗位
   */
  async matchJobsForCandidate(candidateId: string, limit: number = 10): Promise<MatchResult[]> {
    const candidate = await this.candidateRepository.findOne({
      where: { id: candidateId },
    });

    if (!candidate) {
      throw new Error('考生不存在');
    }

    // 获取活跃岗位
    const activeJobs = await this.jobRepository.find({
      where: { isActive: true },
      order: { createdAt: 'DESC' },
    });

    // 计算每个岗位的匹配度
    const matchResults: MatchResult[] = [];

    for (const job of activeJobs) {
      const matchResult = await this.calculateJobMatch(candidate, job);
      matchResults.push(matchResult);
    }

    // 按匹配度排序并返回前N个
    return matchResults
      .sort((a, b) => b.matchScore - a.matchScore)
      .slice(0, limit);
  }

  /**
   * 计算单个岗位的匹配度
   */
  async calculateJobMatch(candidate: Candidate, job: Job): Promise<MatchResult> {
    // 1. 能力匹配度
    const abilityMatch = await this.calculateAbilityMatch(candidate, job);
    
    // 2. 教育背景匹配度
    const educationMatch = this.calculateEducationMatch(candidate, job);
    
    // 3. 经验匹配度
    const experienceMatch = this.calculateExperienceMatch(candidate, job);
    
    // 4. 技能匹配度
    const skillMatch = this.calculateSkillMatch(candidate, job);
    
    // 5. 民族政策加分
    const ethnicPreference = this.calculateEthnicPreference(candidate, job);
    
    // 综合匹配度计算
    const weights = {
      ability: 0.4,
      education: 0.2,
      experience: 0.15,
      skill: 0.15,
      ethnic: 0.1,
    };

    const overallScore = 
      abilityMatch * weights.ability +
      educationMatch * weights.education +
      experienceMatch * weights.experience +
      skillMatch * weights.skill +
      ethnicPreference * weights.ethnic;

    // 竞争度分析
    const competitionLevel = await this.analyzeCompetitionLevel(job);
    
    // 成功概率预测
    const successProbability = await this.abilityService.predictSuccessProbability(
      candidate.id, 
      job
    );

    // 生成推荐理由
    const { strengths, improvements } = this.generateRecommendationReason(
      candidate,
      job,
      {
        abilityMatch,
        educationMatch,
        experienceMatch,
        skillMatch,
        ethnicPreference,
      }
    );

    return {
      job,
      matchScore: Math.round(overallScore * 100) / 100,
      competitionLevel,
      successProbability,
      recommendationReason: {
        abilityMatch: Math.round(abilityMatch * 100) / 100,
        educationMatch: Math.round(educationMatch * 100) / 100,
        experienceMatch: Math.round(experienceMatch * 100) / 100,
        skillMatch: Math.round(skillMatch * 100) / 100,
        ethnicPreference: Math.round(ethnicPreference * 100) / 100,
        overallScore: Math.round(overallScore * 100) / 100,
        strengths,
        improvements,
      },
    };
  }

  /**
   * 分析岗位竞争情况
   */
  async analyzeJobCompetition(jobId: string): Promise<CompetitionAnalysis> {
    const job = await this.jobRepository.findOne({
      where: { id: jobId },
      relations: ['applications'],
    });

    if (!job) {
      throw new Error('岗位不存在');
    }

    const totalApplications = job.applications?.length || 0;
    const competitionRatio = totalApplications / job.recruitmentCount;
    
    // 计算申请者平均分数
    const applicationsWithScores = job.applications?.filter(app => app.matchScore) || [];
    const averageScore = applicationsWithScores.length > 0
      ? applicationsWithScores.reduce((sum, app) => sum + (app.matchScore || 0), 0) / applicationsWithScores.length
      : 0;

    // 确定竞争等级
    let competitionLevel: 'low' | 'medium' | 'high';
    if (competitionRatio < 10) {
      competitionLevel = 'low';
    } else if (competitionRatio < 30) {
      competitionLevel = 'medium';
    } else {
      competitionLevel = 'high';
    }

    return {
      totalApplications,
      competitionRatio: Math.round(competitionRatio * 100) / 100,
      averageScore: Math.round(averageScore * 100) / 100,
      competitionLevel,
      similarCandidates: totalApplications,
    };
  }

  private async calculateAbilityMatch(candidate: Candidate, job: Job): Promise<number> {
    try {
      const abilityProfile = await this.abilityService.analyzeAbilityProfile(candidate.id);
      
      // 根据岗位权重计算能力匹配度
      const weightedScore = 
        (abilityProfile.analyticalAbility * job.analyticalWeight +
         abilityProfile.verbalAbility * job.verbalWeight +
         abilityProfile.numericalAbility * job.numericalWeight +
         abilityProfile.logicalAbility * job.logicalWeight +
         abilityProfile.comprehensiveAbility * job.comprehensiveWeight) / 100;

      return Math.min(weightedScore, 1);
    } catch (error) {
      // 如果无法获取能力评估，返回默认值
      return 0.6;
    }
  }

  private calculateEducationMatch(candidate: Candidate, job: Job): number {
    const educationLevels = {
      'high_school': 1,
      'college': 2,
      'bachelor': 3,
      'master': 4,
      'doctor': 5,
    };

    const candidateLevel = educationLevels[candidate.education] || 0;
    const requiredLevel = educationLevels[job.minEducation] || 0;

    // 满足最低要求得基础分
    if (candidateLevel >= requiredLevel) {
      let score = 0.7;
      
      // 专业匹配加分
      if (job.preferredMajors && job.preferredMajors.includes(candidate.major)) {
        score += 0.3;
      }
      
      return Math.min(score, 1);
    }

    return 0.3; // 不满足最低要求
  }

  private calculateExperienceMatch(candidate: Candidate, job: Job): number {
    // 简化的经验匹配算法
    if (!candidate.workExperience) {
      return job.level === 'entry' ? 0.8 : 0.4;
    }

    // 根据工作经验长度和岗位级别匹配
    const experienceYears = this.extractExperienceYears(candidate.workExperience);
    
    switch (job.level) {
      case 'entry':
        return experienceYears >= 0 ? 0.9 : 0.7;
      case 'intermediate':
        return experienceYears >= 2 ? 0.9 : 0.6;
      case 'senior':
        return experienceYears >= 5 ? 0.9 : 0.5;
      case 'management':
        return experienceYears >= 8 ? 0.9 : 0.4;
      default:
        return 0.6;
    }
  }

  private calculateSkillMatch(candidate: Candidate, job: Job): number {
    if (!job.requiredSkills || !candidate.skills) {
      return 0.5;
    }

    const candidateSkills = candidate.skills.toLowerCase().split(/[,，、\s]+/);
    const requiredSkills = job.requiredSkills.map(skill => skill.toLowerCase());
    
    const matchedSkills = requiredSkills.filter(skill => 
      candidateSkills.some(candidateSkill => 
        candidateSkill.includes(skill) || skill.includes(candidateSkill)
      )
    );

    return matchedSkills.length / requiredSkills.length;
  }

  private calculateEthnicPreference(candidate: Candidate, job: Job): number {
    if (!job.ethnicPreference) {
      return 0;
    }

    if (job.preferredEthnicGroups && job.preferredEthnicGroups.includes(candidate.ethnicGroup)) {
      return 1;
    }

    return candidate.ethnicGroup !== 'han' ? 0.5 : 0;
  }

  private async analyzeCompetitionLevel(job: Job): Promise<'low' | 'medium' | 'high'> {
    const applicationCount = await this.applicationRepository.count({
      where: { jobId: job.id },
    });

    const ratio = applicationCount / job.recruitmentCount;

    if (ratio < 10) return 'low';
    if (ratio < 30) return 'medium';
    return 'high';
  }

  private generateRecommendationReason(
    candidate: Candidate,
    job: Job,
    scores: any
  ): { strengths: string[]; improvements: string[] } {
    const strengths: string[] = [];
    const improvements: string[] = [];

    // 分析优势
    if (scores.abilityMatch >= 0.8) {
      strengths.push('能力水平与岗位要求高度匹配');
    }
    if (scores.educationMatch >= 0.8) {
      strengths.push('教育背景符合岗位要求');
    }
    if (scores.skillMatch >= 0.7) {
      strengths.push('技能与岗位需求匹配度较高');
    }
    if (scores.ethnicPreference > 0) {
      strengths.push('符合民族政策优惠条件');
    }

    // 分析改进点
    if (scores.abilityMatch < 0.6) {
      improvements.push('建议提升相关能力水平');
    }
    if (scores.skillMatch < 0.5) {
      improvements.push('建议学习岗位相关技能');
    }
    if (scores.experienceMatch < 0.5) {
      improvements.push('建议积累相关工作经验');
    }

    return { strengths, improvements };
  }

  private extractExperienceYears(experience: string): number {
    // 简单的经验年限提取逻辑
    const yearMatch = experience.match(/(\d+)\s*年/);
    return yearMatch ? parseInt(yearMatch[1]) : 0;
  }
}
