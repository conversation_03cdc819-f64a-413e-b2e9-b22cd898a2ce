// 用户相关类型
export enum UserRole {
  ADMIN = 'admin',
  CANDIDATE = 'candidate',
  HR = 'hr',
}

export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  role: UserRole;
  isActive: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// 考生相关类型
export enum Education {
  HIGH_SCHOOL = 'high_school',
  COLLEGE = 'college',
  BACHELOR = 'bachelor',
  MASTER = 'master',
  DOCTOR = 'doctor',
}

export enum EthnicGroup {
  HAN = 'han',
  MIAO = 'miao',
  BUYI = 'buyi',
  DONG = 'dong',
  TU = 'tu',
  YI = 'yi',
  GELAO = 'gelao',
  SHUI = 'shui',
  HUI = 'hui',
  ZHUANG = 'zhuang',
  YAO = 'yao',
  BAI = 'bai',
  OTHER = 'other',
}

export interface Candidate {
  id: string;
  name: string;
  idCard: string;
  birthDate: Date;
  education: Education;
  major: string;
  graduateSchool: string;
  graduateDate: Date;
  ethnicGroup: EthnicGroup;
  workExperience?: string;
  skills?: string;
  certifications?: string;
  analyticalAbility?: number;
  verbalAbility?: number;
  numericalAbility?: number;
  logicalAbility?: number;
  comprehensiveAbility?: number;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

// 岗位相关类型
export enum JobType {
  ADMINISTRATIVE = 'administrative',
  TECHNICAL = 'technical',
  EDUCATION = 'education',
  MEDICAL = 'medical',
  OTHER = 'other',
}

export enum JobLevel {
  ENTRY = 'entry',
  INTERMEDIATE = 'intermediate',
  SENIOR = 'senior',
  MANAGEMENT = 'management',
}

export interface Job {
  id: string;
  title: string;
  department: string;
  location: string;
  type: JobType;
  level: JobLevel;
  description: string;
  requirements: string;
  recruitmentCount: number;
  minEducation: Education;
  preferredMajors?: string[];
  requiredSkills?: string[];
  preferredCertifications?: string[];
  ethnicPreference: boolean;
  preferredEthnicGroups?: EthnicGroup[];
  minSalary?: number;
  maxSalary?: number;
  analyticalWeight: number;
  verbalWeight: number;
  numericalWeight: number;
  logicalWeight: number;
  comprehensiveWeight: number;
  registrationStartDate: Date;
  registrationEndDate: Date;
  examDate: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 考试记录类型
export enum ExamType {
  WRITTEN = 'written',
  INTERVIEW = 'interview',
  PRACTICAL = 'practical',
  COMPREHENSIVE = 'comprehensive',
}

export interface ExamRecord {
  id: string;
  examYear: number;
  examType: ExamType;
  examName: string;
  publicBasicScore?: number;
  professionalScore?: number;
  writingScore?: number;
  interviewScore?: number;
  totalScore: number;
  ranking?: number;
  questionAnalysis?: {
    correctCount: number;
    totalCount: number;
    timeSpent: number;
    difficultyDistribution: {
      easy: number;
      medium: number;
      hard: number;
    };
    categoryPerformance: {
      [category: string]: {
        correct: number;
        total: number;
      };
    };
  };
  examDate: Date;
  candidateId: string;
  createdAt: Date;
  updatedAt: Date;
}

// 求职申请类型
export enum ApplicationStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  WITHDRAWN = 'withdrawn',
}

export interface JobApplication {
  id: string;
  status: ApplicationStatus;
  coverLetter?: string;
  matchScore?: number;
  recommendationReason?: {
    abilityMatch: number;
    educationMatch: number;
    experienceMatch: number;
    skillMatch: number;
    ethnicPreference: number;
    overallScore: number;
    strengths: string[];
    improvements: string[];
  };
  isRecommended: boolean;
  appliedAt?: Date;
  reviewedAt?: Date;
  candidateId: string;
  jobId: string;
  candidate?: Candidate;
  job?: Job;
  createdAt: Date;
  updatedAt: Date;
}

// 推荐结果类型
export interface RecommendationResult {
  job: Job;
  matchScore: number;
  recommendationReason: {
    abilityMatch: number;
    educationMatch: number;
    experienceMatch: number;
    skillMatch: number;
    ethnicPreference: number;
    overallScore: number;
    strengths: string[];
    improvements: string[];
  };
  competitionLevel: 'low' | 'medium' | 'high';
  successProbability: number;
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    message: string;
    details?: any;
  };
}

// 分页类型
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 统计数据类型
export interface DashboardStats {
  totalJobs: number;
  activeJobs: number;
  totalApplications: number;
  recommendedJobs: number;
  averageMatchScore: number;
  recentActivity: {
    date: string;
    applications: number;
    recommendations: number;
  }[];
}

// 筛选条件类型
export interface JobFilters {
  type?: JobType[];
  level?: JobLevel[];
  location?: string[];
  education?: Education[];
  salaryRange?: [number, number];
  ethnicPreference?: boolean;
  keyword?: string;
}
