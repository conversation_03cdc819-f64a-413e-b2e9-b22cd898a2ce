import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import { Card, Typography, Tag, Space } from 'antd';
import { FireOutlined, ThunderboltOutlined, SnowflakeOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface JobHeatData {
  jobId: string;
  jobTitle: string;
  department: string;
  location: string;
  type: string;
  applications: number;
  recruitmentCount: number;
  competitionRatio: number;
  heatLevel: 'hot' | 'warm' | 'cold';
}

interface JobHeatmapChartProps {
  data: JobHeatData[];
  loading?: boolean;
  height?: number;
}

const JobHeatmapChart: React.FC<JobHeatmapChartProps> = ({
  data,
  loading = false,
  height = 400,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (!chartRef.current || loading) return;

    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
    }

    const chart = chartInstance.current;

    if (!data || data.length === 0) {
      chart.clear();
      return;
    }

    // 按地区和类型分组数据
    const locationTypes = new Set<string>();
    const jobTypes = new Set<string>();
    const heatmapData: any[] = [];

    data.forEach(job => {
      locationTypes.add(job.location);
      jobTypes.add(job.type);
    });

    const locations = Array.from(locationTypes);
    const types = Array.from(jobTypes);

    // 创建热力图数据
    locations.forEach((location, locationIndex) => {
      types.forEach((type, typeIndex) => {
        const jobsInCategory = data.filter(job => 
          job.location === location && job.type === type
        );
        
        if (jobsInCategory.length > 0) {
          const avgCompetition = jobsInCategory.reduce((sum, job) => 
            sum + job.competitionRatio, 0
          ) / jobsInCategory.length;
          
          heatmapData.push([
            typeIndex,
            locationIndex,
            Math.round(avgCompetition * 10) / 10,
            jobsInCategory.length
          ]);
        } else {
          heatmapData.push([typeIndex, locationIndex, 0, 0]);
        }
      });
    });

    const option: echarts.EChartsOption = {
      title: {
        text: '岗位竞争热度分布',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        position: 'top',
        formatter: (params: any) => {
          const typeIndex = params.data[0];
          const locationIndex = params.data[1];
          const competition = params.data[2];
          const jobCount = params.data[3];
          
          return `
            <div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">
                ${types[typeIndex]} - ${locations[locationIndex]}
              </div>
              <div>岗位数量: ${jobCount}</div>
              <div>平均竞争比: ${competition}:1</div>
            </div>
          `;
        },
      },
      grid: {
        height: '60%',
        top: '15%',
        left: '15%',
        right: '10%',
      },
      xAxis: {
        type: 'category',
        data: types.map(type => {
          const typeMap: { [key: string]: string } = {
            'administrative': '行政类',
            'technical': '技术类',
            'education': '教育类',
            'medical': '医疗类',
            'other': '其他类',
          };
          return typeMap[type] || type;
        }),
        splitArea: {
          show: true,
        },
        axisLabel: {
          fontSize: 11,
        },
      },
      yAxis: {
        type: 'category',
        data: locations,
        splitArea: {
          show: true,
        },
        axisLabel: {
          fontSize: 11,
        },
      },
      visualMap: {
        min: 0,
        max: 50,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '5%',
        inRange: {
          color: ['#e6f7ff', '#bae7ff', '#91d5ff', '#69c0ff', '#40a9ff', '#1890ff', '#096dd9', '#0050b3'],
        },
        text: ['高竞争', '低竞争'],
        textStyle: {
          fontSize: 11,
        },
      },
      series: [
        {
          name: '竞争热度',
          type: 'heatmap',
          data: heatmapData,
          label: {
            show: true,
            formatter: (params: any) => {
              const competition = params.data[2];
              return competition > 0 ? competition.toString() : '';
            },
            fontSize: 10,
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    };

    chart.setOption(option);

    const handleResize = () => {
      chart.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [data, loading, height]);

  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, []);

  const getHeatLevelIcon = (level: string) => {
    switch (level) {
      case 'hot':
        return <FireOutlined style={{ color: '#f5222d' }} />;
      case 'warm':
        return <ThunderboltOutlined style={{ color: '#faad14' }} />;
      case 'cold':
        return <SnowflakeOutlined style={{ color: '#52c41a' }} />;
      default:
        return null;
    }
  };

  const getHeatLevelText = (level: string) => {
    switch (level) {
      case 'hot':
        return '竞争激烈';
      case 'warm':
        return '竞争适中';
      case 'cold':
        return '竞争较小';
      default:
        return '未知';
    }
  };

  const getHeatLevelColor = (level: string) => {
    switch (level) {
      case 'hot':
        return '#f5222d';
      case 'warm':
        return '#faad14';
      case 'cold':
        return '#52c41a';
      default:
        return '#666';
    }
  };

  // 统计各热度级别的岗位数量
  const heatStats = data.reduce((stats, job) => {
    stats[job.heatLevel] = (stats[job.heatLevel] || 0) + 1;
    return stats;
  }, {} as { [key: string]: number });

  return (
    <Card>
      <div style={{ marginBottom: 16 }}>
        <Title level={4} style={{ margin: 0 }}>
          岗位竞争热度分析
        </Title>
        <Text type="secondary">
          不同地区和类型岗位的竞争激烈程度
        </Text>
      </div>

      <div ref={chartRef} style={{ height: `${height}px`, width: '100%' }} />

      <div style={{ marginTop: 16 }}>
        <Space size="large">
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: 20, fontWeight: 'bold', color: '#f5222d' }}>
              {heatStats.hot || 0}
            </div>
            <div style={{ fontSize: 12, color: '#666' }}>
              <FireOutlined style={{ marginRight: 4 }} />
              热门岗位
            </div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: 20, fontWeight: 'bold', color: '#faad14' }}>
              {heatStats.warm || 0}
            </div>
            <div style={{ fontSize: 12, color: '#666' }}>
              <ThunderboltOutlined style={{ marginRight: 4 }} />
              适中岗位
            </div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: 20, fontWeight: 'bold', color: '#52c41a' }}>
              {heatStats.cold || 0}
            </div>
            <div style={{ fontSize: 12, color: '#666' }}>
              <SnowflakeOutlined style={{ marginRight: 4 }} />
              冷门岗位
            </div>
          </div>
        </Space>
      </div>

      {data.length > 0 && (
        <div style={{ marginTop: 16 }}>
          <Text strong>热度排行：</Text>
          <div style={{ marginTop: 8 }}>
            {data
              .sort((a, b) => b.competitionRatio - a.competitionRatio)
              .slice(0, 5)
              .map((job, index) => (
                <div key={job.jobId} style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'center',
                  padding: '4px 0',
                  borderBottom: index < 4 ? '1px solid #f0f0f0' : 'none'
                }}>
                  <div style={{ flex: 1 }}>
                    <Text strong>{job.jobTitle}</Text>
                    <Text type="secondary" style={{ marginLeft: 8, fontSize: 12 }}>
                      {job.department}
                    </Text>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <Tag color={getHeatLevelColor(job.heatLevel)} icon={getHeatLevelIcon(job.heatLevel)}>
                      {job.competitionRatio}:1
                    </Tag>
                  </div>
                </div>
              ))}
          </div>
        </div>
      )}
    </Card>
  );
};

export default JobHeatmapChart;
