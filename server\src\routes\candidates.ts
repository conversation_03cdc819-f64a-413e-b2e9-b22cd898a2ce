import { Router } from 'express';
import { CandidateController } from '../controllers/CandidateController';
import { authenticateToken, requireCandidate } from '../middleware/auth';

const router = Router();
const candidateController = new CandidateController();

// 所有路由都需要认证
router.use(authenticateToken);

// 考生信息管理
router.get('/profile', candidateController.getProfile);
router.post('/profile', candidateController.createProfile);
router.put('/profile', candidateController.updateProfile);

// 考试记录管理
router.get('/exam-records', candidateController.getExamRecords);
router.post('/exam-records', candidateController.addExamRecord);
router.put('/exam-records/:id', candidateController.updateExamRecord);
router.delete('/exam-records/:id', candidateController.deleteExamRecord);

// 求职申请管理
router.get('/applications', candidateController.getApplications);
router.post('/applications', candidateController.createApplication);
router.put('/applications/:id', candidateController.updateApplication);
router.delete('/applications/:id', candidateController.deleteApplication);

export default router;
