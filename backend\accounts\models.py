from django.contrib.auth.models import AbstractUser
from django.db import models

class User(AbstractUser):
    """自定义用户模型"""
    
    class Role(models.TextChoices):
        ADMIN = 'admin', '管理员'
        HR = 'hr', 'HR'
        CANDIDATE = 'candidate', '考生'
    
    email = models.EmailField('邮箱', unique=True)
    name = models.CharField('姓名', max_length=100)
    phone = models.CharField('手机号', max_length=20, blank=True, null=True)
    role = models.Char<PERSON>ield('角色', max_length=20, choices=Role.choices, default=Role.CANDIDATE)
    is_active = models.BooleanField('是否激活', default=True)
    avatar = models.ImageField('头像', upload_to='avatars/', blank=True, null=True)
    last_login_at = models.DateTimeField('最后登录时间', blank=True, null=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'name']
    
    class Meta:
        db_table = 'users'
        verbose_name = '用户'
        verbose_name_plural = '用户'
    
    def __str__(self):
        return f"{self.name} ({self.email})"
    
    @property
    def is_admin(self):
        return self.role == self.Role.ADMIN
    
    @property
    def is_hr(self):
        return self.role == self.Role.HR
    
    @property
    def is_candidate(self):
        return self.role == self.Role.CANDIDATE
