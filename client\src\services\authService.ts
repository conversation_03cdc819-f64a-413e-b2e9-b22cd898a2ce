import { apiService } from './api';
import { User } from '../types';

interface LoginRequest {
  email: string;
  password: string;
}

interface RegisterRequest {
  email: string;
  password: string;
  name: string;
  phone?: string;
}

interface AuthResponse {
  user: User;
  token: string;
}

class AuthService {
  // 用户登录
  async login(email: string, password: string): Promise<AuthResponse> {
    const response = await apiService.post<AuthResponse>('/auth/login', {
      email,
      password,
    });
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || '登录失败');
    }
    
    return response.data;
  }

  // 用户注册
  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await apiService.post<AuthResponse>('/auth/register', userData);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || '注册失败');
    }
    
    return response.data;
  }

  // 获取当前用户信息
  async getCurrentUser(): Promise<User> {
    const response = await apiService.get<User>('/auth/me');
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || '获取用户信息失败');
    }
    
    return response.data;
  }

  // 更新用户信息
  async updateProfile(userData: Partial<User>): Promise<User> {
    const response = await apiService.put<User>('/auth/profile', userData);
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || '更新用户信息失败');
    }
    
    return response.data;
  }

  // 修改密码
  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    const response = await apiService.post('/auth/change-password', {
      currentPassword,
      newPassword,
    });
    
    if (!response.success) {
      throw new Error(response.error?.message || '修改密码失败');
    }
  }

  // 忘记密码
  async forgotPassword(email: string): Promise<void> {
    const response = await apiService.post('/auth/forgot-password', { email });
    
    if (!response.success) {
      throw new Error(response.error?.message || '发送重置邮件失败');
    }
  }

  // 重置密码
  async resetPassword(token: string, newPassword: string): Promise<void> {
    const response = await apiService.post('/auth/reset-password', {
      token,
      newPassword,
    });
    
    if (!response.success) {
      throw new Error(response.error?.message || '重置密码失败');
    }
  }

  // 刷新token
  async refreshToken(): Promise<AuthResponse> {
    const response = await apiService.post<AuthResponse>('/auth/refresh');
    
    if (!response.success || !response.data) {
      throw new Error(response.error?.message || '刷新token失败');
    }
    
    return response.data;
  }

  // 登出
  async logout(): Promise<void> {
    try {
      await apiService.post('/auth/logout');
    } catch (error) {
      // 即使服务器端登出失败，也要清除本地token
      console.warn('服务器端登出失败:', error);
    } finally {
      localStorage.removeItem('token');
    }
  }
}

export const authService = new AuthService();
