import { Request, Response, NextFunction } from 'express';
import compression from 'compression';
import rateLimit from 'express-rate-limit';

// 响应压缩中间件
export const compressionMiddleware = compression({
  filter: (req: Request, res: Response) => {
    if (req.headers['x-no-compression']) {
      return false;
    }
    return compression.filter(req, res);
  },
  level: 6, // 压缩级别 1-9
  threshold: 1024, // 只压缩大于1KB的响应
});

// API限流中间件
export const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP最多100个请求
  message: {
    success: false,
    error: {
      message: '请求过于频繁，请稍后再试',
    },
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 严格限流（用于登录等敏感操作）
export const strictLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 每个IP最多5次尝试
  message: {
    success: false,
    error: {
      message: '尝试次数过多，请15分钟后再试',
    },
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 缓存控制中间件
export const cacheControl = (maxAge: number = 300) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (req.method === 'GET') {
      res.set('Cache-Control', `public, max-age=${maxAge}`);
    } else {
      res.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    }
    next();
  };
};

// 响应时间记录中间件
export const responseTimeLogger = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    
    // 记录慢查询
    if (duration > 1000) {
      console.warn(`Slow request: ${req.method} ${req.path} - ${duration}ms`);
    }
    
    // 在开发环境下记录所有请求时间
    if (process.env.NODE_ENV === 'development') {
      console.log(`${req.method} ${req.path} - ${duration}ms`);
    }
  });
  
  next();
};

// 内存使用监控
export const memoryMonitor = () => {
  setInterval(() => {
    const memUsage = process.memoryUsage();
    const memUsageMB = {
      rss: Math.round(memUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024),
    };
    
    // 内存使用超过500MB时警告
    if (memUsageMB.heapUsed > 500) {
      console.warn('High memory usage:', memUsageMB);
    }
    
    // 在开发环境下定期输出内存使用情况
    if (process.env.NODE_ENV === 'development') {
      console.log('Memory usage:', memUsageMB);
    }
  }, 60000); // 每分钟检查一次
};

// 数据库查询优化配置
export const dbOptimization = {
  // 连接池配置
  connectionPool: {
    max: 20, // 最大连接数
    min: 5,  // 最小连接数
    idle: 10000, // 空闲超时时间
    acquire: 60000, // 获取连接超时时间
  },
  
  // 查询超时配置
  queryTimeout: 30000, // 30秒
  
  // 慢查询阈值
  slowQueryThreshold: 1000, // 1秒
};

// 推荐算法性能配置
export const recommendationConfig = {
  // 缓存配置
  cache: {
    ttl: 3600, // 缓存1小时
    maxSize: 1000, // 最大缓存条目数
  },
  
  // 批处理配置
  batchSize: 50, // 批处理大小
  
  // 并发限制
  maxConcurrency: 5, // 最大并发推荐请求数
  
  // 超时配置
  timeout: 10000, // 10秒超时
};

// 文件上传配置
export const uploadConfig = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedMimeTypes: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'text/csv',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ],
  uploadPath: process.env.UPLOAD_PATH || './uploads',
};

// 安全配置
export const securityConfig = {
  // JWT配置
  jwt: {
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    algorithm: 'HS256',
  },
  
  // 密码策略
  password: {
    minLength: 6,
    maxLength: 128,
    requireNumbers: false,
    requireSymbols: false,
    requireUppercase: false,
  },
  
  // CORS配置
  cors: {
    origin: process.env.NODE_ENV === 'production' 
      ? process.env.FRONTEND_URL 
      : ['http://localhost:3000', 'http://127.0.0.1:3000'],
    credentials: true,
    optionsSuccessStatus: 200,
  },
};

// 日志配置
export const loggingConfig = {
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  format: process.env.NODE_ENV === 'production' ? 'combined' : 'dev',
  
  // 日志轮转配置
  rotation: {
    maxSize: '20m',
    maxFiles: '14d',
    datePattern: 'YYYY-MM-DD',
  },
};

// 健康检查配置
export const healthCheckConfig = {
  interval: 30000, // 30秒检查一次
  timeout: 5000,   // 5秒超时
  
  checks: {
    database: true,
    memory: true,
    disk: false, // 在简单部署中禁用磁盘检查
  },
  
  thresholds: {
    memoryUsage: 0.9,  // 90%内存使用率
    responseTime: 1000, // 1秒响应时间
  },
};

// 性能监控配置
export const monitoringConfig = {
  enabled: process.env.NODE_ENV === 'production',
  
  metrics: {
    requestDuration: true,
    requestCount: true,
    errorRate: true,
    memoryUsage: true,
  },
  
  alerts: {
    errorRateThreshold: 0.05, // 5%错误率
    responseTimeThreshold: 2000, // 2秒响应时间
    memoryThreshold: 0.85, // 85%内存使用率
  },
};

// 导出所有配置
export const performanceConfig = {
  db: dbOptimization,
  recommendation: recommendationConfig,
  upload: uploadConfig,
  security: securityConfig,
  logging: loggingConfig,
  healthCheck: healthCheckConfig,
  monitoring: monitoringConfig,
};
