# 快速启动指南

## 🚀 5分钟快速体验

### 前提条件
- Node.js 18+ 
- PostgreSQL 13+
- Git

### 1. 克隆项目
```bash
git clone <your-repo-url>
cd 毕设
```

### 2. 自动设置
```bash
npm run setup
```

### 3. 配置数据库
创建PostgreSQL数据库：
```bash
createdb recruitment_recommendation
```

或者使用PostgreSQL客户端：
```sql
CREATE DATABASE recruitment_recommendation;
```

### 4. 初始化数据库
```bash
cd server
npm run init-db
```

### 5. 启动开发服务器
```bash
npm run dev
```

### 6. 访问应用
- 前端: http://localhost:3000
- 后端API: http://localhost:5000

### 7. 登录测试
使用以下账号登录：
- 邮箱: `<EMAIL>`
- 密码: `admin123`

## 🎯 主要功能

### 已实现功能
- ✅ 用户注册/登录系统
- ✅ 基础界面框架
- ✅ 数据库模型设计
- ✅ API路由结构
- ✅ 认证中间件
- ✅ 示例数据初始化

### 开发中功能
- 🔄 考生信息管理
- 🔄 岗位管理系统
- 🔄 智能推荐算法
- 🔄 数据分析模块
- 🔄 可视化图表

## 📁 项目结构

```
毕设/
├── client/                 # React前端
│   ├── src/
│   │   ├── components/     # 组件
│   │   ├── pages/         # 页面
│   │   ├── services/      # API服务
│   │   └── types/         # 类型定义
├── server/                # Node.js后端
│   ├── src/
│   │   ├── entities/      # 数据模型
│   │   ├── controllers/   # 控制器
│   │   ├── routes/        # 路由
│   │   ├── middleware/    # 中间件
│   │   └── scripts/       # 脚本
├── docs/                  # 文档
└── scripts/               # 工具脚本
```

## 🛠️ 开发命令

### 根目录命令
```bash
npm run setup          # 初始化项目
npm run dev            # 启动开发服务器
npm run build          # 构建生产版本
npm run install:all    # 安装所有依赖
```

### 后端命令
```bash
cd server
npm run dev            # 启动后端开发服务器
npm run build          # 构建后端
npm run init-db        # 初始化数据库
npm run lint           # 代码检查
```

### 前端命令
```bash
cd client
npm run dev            # 启动前端开发服务器
npm run build          # 构建前端
npm run lint           # 代码检查
```

## 🔧 配置说明

### 环境变量
- `server/.env` - 后端配置
- `client/.env` - 前端配置

### 数据库配置
修改 `server/.env` 中的数据库连接信息：
```env
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_password
DB_DATABASE=recruitment_recommendation
```

## 📚 API文档
详细API文档请查看 [docs/API.md](docs/API.md)

## 🚀 部署指南
生产环境部署请查看 [docs/DEPLOYMENT.md](docs/DEPLOYMENT.md)

## ❓ 常见问题

### Q: 数据库连接失败
A: 检查PostgreSQL是否启动，数据库是否存在，连接信息是否正确

### Q: 端口被占用
A: 修改 `.env` 文件中的端口配置

### Q: 依赖安装失败
A: 尝试删除 `node_modules` 文件夹后重新安装

## 📞 技术支持
如有问题，请查看项目文档或提交Issue。
