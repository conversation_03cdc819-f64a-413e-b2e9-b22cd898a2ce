import { Response } from 'express';
import { AuthRequest } from '../middleware/auth';
import { AppDataSource } from '../config/database';
import { Candidate } from '../entities/Candidate';
import { RecommendationEngine } from '../services/RecommendationEngine';

export class RecommendationController {
  private candidateRepository = AppDataSource.getRepository(Candidate);
  private recommendationEngine = new RecommendationEngine();

  // 获取推荐岗位
  getRecommendations = async (req: AuthRequest, res: Response) => {
    try {
      const userId = req.user!.id;
      const { limit = 10, minScore = 0.3 } = req.query;

      // 获取考生信息
      const candidate = await this.candidateRepository.findOne({
        where: { userId },
      });

      if (!candidate) {
        return res.status(404).json({
          success: false,
          error: { message: '考生信息不存在，请先完善个人资料' },
        });
      }

      // 生成推荐
      const recommendations = await this.recommendationEngine.generateRecommendations(
        candidate.id,
        {
          maxResults: parseInt(limit as string),
          minScore: parseFloat(minScore as string),
        }
      );

      res.json({
        success: true,
        data: recommendations,
        message: `为您推荐了 ${recommendations.length} 个匹配岗位`,
      });
    } catch (error) {
      throw error;
    }
  };

  // 刷新推荐
  refreshRecommendations = async (req: AuthRequest, res: Response) => {
    try {
      const userId = req.user!.id;
      const {
        diversityWeight = 0.3,
        noveltyWeight = 0.2,
        limit = 10
      } = req.body;

      const candidate = await this.candidateRepository.findOne({
        where: { userId },
      });

      if (!candidate) {
        return res.status(404).json({
          success: false,
          error: { message: '考生信息不存在' },
        });
      }

      // 使用不同的参数重新生成推荐
      const recommendations = await this.recommendationEngine.generateRecommendations(
        candidate.id,
        {
          maxResults: limit,
          diversityWeight,
          noveltyWeight,
          minScore: 0.2, // 刷新时降低最低分数要求
        }
      );

      res.json({
        success: true,
        data: recommendations,
        message: '推荐已刷新',
      });
    } catch (error) {
      throw error;
    }
  };

  // 提交推荐反馈
  submitFeedback = async (req: AuthRequest, res: Response) => {
    try {
      const userId = req.user!.id;
      const { jobId } = req.params;
      const { rating, feedback, action } = req.body;

      const candidate = await this.candidateRepository.findOne({
        where: { userId },
      });

      if (!candidate) {
        return res.status(404).json({
          success: false,
          error: { message: '考生信息不存在' },
        });
      }

      // 这里可以保存反馈数据用于改进推荐算法
      // 暂时只返回成功响应
      console.log('推荐反馈:', {
        candidateId: candidate.id,
        jobId,
        rating,
        feedback,
        action,
        timestamp: new Date(),
      });

      res.json({
        success: true,
        message: '感谢您的反馈，这将帮助我们改进推荐质量',
      });
    } catch (error) {
      throw error;
    }
  };

  // 获取推荐解释
  getRecommendationExplanation = async (req: AuthRequest, res: Response) => {
    try {
      const userId = req.user!.id;
      const { jobId } = req.params;

      const candidate = await this.candidateRepository.findOne({
        where: { userId },
      });

      if (!candidate) {
        return res.status(404).json({
          success: false,
          error: { message: '考生信息不存在' },
        });
      }

      // 重新生成推荐以获取详细解释
      const recommendations = await this.recommendationEngine.generateRecommendations(
        candidate.id,
        { maxResults: 50 }
      );

      const targetRecommendation = recommendations.find(rec => rec.job.id === jobId);

      if (!targetRecommendation) {
        return res.status(404).json({
          success: false,
          error: { message: '未找到该岗位的推荐信息' },
        });
      }

      res.json({
        success: true,
        data: {
          jobTitle: targetRecommendation.job.title,
          score: targetRecommendation.score,
          confidence: targetRecommendation.confidence,
          reason: targetRecommendation.reason,
          tags: targetRecommendation.tags,
          competitionLevel: targetRecommendation.competitionLevel,
          successProbability: targetRecommendation.successProbability,
          explanation: targetRecommendation.explanation,
        },
      });
    } catch (error) {
      throw error;
    }
  };
}
