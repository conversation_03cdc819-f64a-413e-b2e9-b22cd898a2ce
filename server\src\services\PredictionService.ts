import { AppDataSource } from '../config/database';
import { Job } from '../entities/Job';
import { JobApplication } from '../entities/JobApplication';
import { Candidate } from '../entities/Candidate';
import { ExamRecord } from '../entities/ExamRecord';

export interface JobPrediction {
  jobId: string;
  jobTitle: string;
  predictedApplications: number;
  currentApplications: number;
  competitionLevel: 'low' | 'medium' | 'high';
  trend: 'increasing' | 'stable' | 'decreasing';
  recommendedAction: string;
  riskLevel: 'low' | 'medium' | 'high';
}

export interface TrendAnalysis {
  period: string;
  totalJobs: number;
  totalApplications: number;
  averageCompetition: number;
  hotCategories: {
    category: string;
    count: number;
    growth: number;
  }[];
  coldSpots: {
    jobId: string;
    title: string;
    applications: number;
    recruitmentCount: number;
  }[];
}

export interface CandidateSuccessPrediction {
  candidateId: string;
  overallSuccessRate: number;
  recommendedJobs: {
    jobId: string;
    successProbability: number;
    competitionLevel: string;
  }[];
  riskFactors: string[];
  improvementSuggestions: string[];
}

export class PredictionService {
  private jobRepository = AppDataSource.getRepository(Job);
  private applicationRepository = AppDataSource.getRepository(JobApplication);
  private candidateRepository = AppDataSource.getRepository(Candidate);
  private examRecordRepository = AppDataSource.getRepository(ExamRecord);

  /**
   * 预测岗位报考热度
   */
  async predictJobApplications(timeframe: 'week' | 'month' | 'season' = 'month'): Promise<JobPrediction[]> {
    const activeJobs = await this.jobRepository.find({
      where: { isActive: true },
      relations: ['applications'],
    });

    const predictions: JobPrediction[] = [];

    for (const job of activeJobs) {
      const currentApplications = job.applications?.length || 0;
      const historicalData = await this.getHistoricalApplicationData(job.id);
      
      const predictedApplications = this.calculatePredictedApplications(
        currentApplications,
        historicalData,
        timeframe
      );

      const competitionLevel = this.determineCompetitionLevel(
        predictedApplications,
        job.recruitmentCount
      );

      const trend = this.analyzeTrend(historicalData);
      const riskLevel = this.assessRiskLevel(predictedApplications, job.recruitmentCount);
      const recommendedAction = this.generateRecommendedAction(competitionLevel, trend, riskLevel);

      predictions.push({
        jobId: job.id,
        jobTitle: job.title,
        predictedApplications,
        currentApplications,
        competitionLevel,
        trend,
        recommendedAction,
        riskLevel,
      });
    }

    return predictions.sort((a, b) => b.predictedApplications - a.predictedApplications);
  }

  /**
   * 分析报考趋势
   */
  async analyzeTrends(year: number): Promise<TrendAnalysis> {
    const startDate = new Date(year, 0, 1);
    const endDate = new Date(year, 11, 31);

    // 获取年度数据
    const yearJobs = await this.jobRepository.find({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        } as any,
      },
      relations: ['applications'],
    });

    const totalJobs = yearJobs.length;
    const totalApplications = yearJobs.reduce((sum, job) => sum + (job.applications?.length || 0), 0);
    const averageCompetition = totalApplications / totalJobs;

    // 分析热门类别
    const categoryStats = this.analyzeCategoryTrends(yearJobs);
    const hotCategories = categoryStats
      .sort((a, b) => b.growth - a.growth)
      .slice(0, 5);

    // 识别冷门岗位
    const coldSpots = yearJobs
      .filter(job => {
        const applications = job.applications?.length || 0;
        const ratio = applications / job.recruitmentCount;
        return ratio < 5; // 竞争比小于5:1的岗位
      })
      .map(job => ({
        jobId: job.id,
        title: job.title,
        applications: job.applications?.length || 0,
        recruitmentCount: job.recruitmentCount,
      }))
      .sort((a, b) => a.applications - b.applications)
      .slice(0, 10);

    return {
      period: `${year}年`,
      totalJobs,
      totalApplications,
      averageCompetition: Math.round(averageCompetition * 100) / 100,
      hotCategories,
      coldSpots,
    };
  }

  /**
   * 预测考生成功率
   */
  async predictCandidateSuccess(candidateId: string): Promise<CandidateSuccessPrediction> {
    const candidate = await this.candidateRepository.findOne({
      where: { id: candidateId },
      relations: ['examRecords', 'applications'],
    });

    if (!candidate) {
      throw new Error('考生不存在');
    }

    // 基于历史表现计算整体成功率
    const overallSuccessRate = await this.calculateOverallSuccessRate(candidate);

    // 推荐适合的岗位
    const recommendedJobs = await this.getRecommendedJobsWithProbability(candidate);

    // 识别风险因素
    const riskFactors = this.identifyRiskFactors(candidate);

    // 生成改进建议
    const improvementSuggestions = this.generateImprovementSuggestions(candidate, riskFactors);

    return {
      candidateId,
      overallSuccessRate,
      recommendedJobs,
      riskFactors,
      improvementSuggestions,
    };
  }

  /**
   * 预测"过热"和"过冷"岗位
   */
  async identifyHotAndColdJobs(): Promise<{
    hotJobs: JobPrediction[];
    coldJobs: JobPrediction[];
  }> {
    const predictions = await this.predictJobApplications();

    const hotJobs = predictions.filter(p => 
      p.competitionLevel === 'high' && p.trend === 'increasing'
    ).slice(0, 10);

    const coldJobs = predictions.filter(p => 
      p.competitionLevel === 'low' && p.currentApplications < p.predictedApplications * 0.5
    ).slice(0, 10);

    return { hotJobs, coldJobs };
  }

  private async getHistoricalApplicationData(jobId: string): Promise<number[]> {
    // 模拟历史数据，实际应用中应该从数据库获取
    // 这里返回过去几个月的申请数据
    const applications = await this.applicationRepository.find({
      where: { jobId },
      order: { createdAt: 'ASC' },
    });

    // 按月分组统计
    const monthlyData: { [key: string]: number } = {};
    applications.forEach(app => {
      const month = app.createdAt.toISOString().slice(0, 7); // YYYY-MM
      monthlyData[month] = (monthlyData[month] || 0) + 1;
    });

    return Object.values(monthlyData);
  }

  private calculatePredictedApplications(
    current: number,
    historical: number[],
    timeframe: string
  ): number {
    if (historical.length < 2) {
      // 如果历史数据不足，使用简单的增长模型
      return Math.round(current * 1.2);
    }

    // 计算平均增长率
    let totalGrowth = 0;
    for (let i = 1; i < historical.length; i++) {
      if (historical[i - 1] > 0) {
        totalGrowth += (historical[i] - historical[i - 1]) / historical[i - 1];
      }
    }

    const avgGrowthRate = totalGrowth / (historical.length - 1);
    
    // 根据时间框架调整预测
    const timeMultiplier = timeframe === 'week' ? 0.25 : timeframe === 'month' ? 1 : 3;
    
    return Math.round(current * (1 + avgGrowthRate * timeMultiplier));
  }

  private determineCompetitionLevel(applications: number, recruitmentCount: number): 'low' | 'medium' | 'high' {
    const ratio = applications / recruitmentCount;
    
    if (ratio < 10) return 'low';
    if (ratio < 30) return 'medium';
    return 'high';
  }

  private analyzeTrend(historical: number[]): 'increasing' | 'stable' | 'decreasing' {
    if (historical.length < 2) return 'stable';

    const recent = historical.slice(-3);
    const earlier = historical.slice(0, -3);

    if (recent.length === 0 || earlier.length === 0) return 'stable';

    const recentAvg = recent.reduce((sum, val) => sum + val, 0) / recent.length;
    const earlierAvg = earlier.reduce((sum, val) => sum + val, 0) / earlier.length;

    const changeRate = (recentAvg - earlierAvg) / earlierAvg;

    if (changeRate > 0.1) return 'increasing';
    if (changeRate < -0.1) return 'decreasing';
    return 'stable';
  }

  private assessRiskLevel(predicted: number, recruitmentCount: number): 'low' | 'medium' | 'high' {
    const ratio = predicted / recruitmentCount;
    
    if (ratio > 50) return 'high';
    if (ratio > 20) return 'medium';
    return 'low';
  }

  private generateRecommendedAction(
    competition: string,
    trend: string,
    risk: string
  ): string {
    if (competition === 'high' && trend === 'increasing') {
      return '竞争激烈且持续升温，建议谨慎报考';
    }
    if (competition === 'low' && trend === 'stable') {
      return '竞争适中，是较好的报考选择';
    }
    if (competition === 'medium' && trend === 'decreasing') {
      return '竞争趋于缓解，可考虑报考';
    }
    return '建议持续关注报考动态';
  }

  private analyzeCategoryTrends(jobs: Job[]): { category: string; count: number; growth: number }[] {
    const categoryStats: { [key: string]: { count: number; growth: number } } = {};

    jobs.forEach(job => {
      const category = job.type;
      if (!categoryStats[category]) {
        categoryStats[category] = { count: 0, growth: 0 };
      }
      categoryStats[category].count++;
      // 简化的增长率计算
      categoryStats[category].growth = Math.random() * 0.5 - 0.1; // -10% to +40%
    });

    return Object.entries(categoryStats).map(([category, stats]) => ({
      category,
      count: stats.count,
      growth: Math.round(stats.growth * 100) / 100,
    }));
  }

  private async calculateOverallSuccessRate(candidate: Candidate): Promise<number> {
    const examRecords = candidate.examRecords || [];
    
    if (examRecords.length === 0) {
      return 0.5; // 默认50%成功率
    }

    // 基于考试成绩计算成功率
    const avgScore = examRecords.reduce((sum, record) => sum + record.totalScore, 0) / examRecords.length;
    
    // 简化的成功率计算：分数越高，成功率越高
    const baseRate = Math.min(avgScore / 100, 0.9);
    
    // 考虑改进趋势
    const trendBonus = this.calculateTrendBonus(examRecords);
    
    return Math.round((baseRate + trendBonus) * 100) / 100;
  }

  private async getRecommendedJobsWithProbability(candidate: Candidate) {
    // 简化实现，实际应该调用JobMatchingService
    const activeJobs = await this.jobRepository.find({
      where: { isActive: true },
      take: 5,
    });

    return activeJobs.map(job => ({
      jobId: job.id,
      successProbability: Math.random() * 0.4 + 0.4, // 40%-80%
      competitionLevel: this.determineCompetitionLevel(10, job.recruitmentCount),
    }));
  }

  private identifyRiskFactors(candidate: Candidate): string[] {
    const risks: string[] = [];

    if (!candidate.examRecords || candidate.examRecords.length === 0) {
      risks.push('缺乏考试记录，难以准确评估能力');
    }

    if (!candidate.workExperience) {
      risks.push('缺乏工作经验可能影响竞争力');
    }

    if (!candidate.skills) {
      risks.push('技能信息不完整');
    }

    const avgScore = candidate.examRecords?.reduce((sum, record) => sum + record.totalScore, 0) / (candidate.examRecords?.length || 1);
    if (avgScore < 60) {
      risks.push('历史考试成绩偏低');
    }

    return risks;
  }

  private generateImprovementSuggestions(candidate: Candidate, risks: string[]): string[] {
    const suggestions: string[] = [];

    risks.forEach(risk => {
      switch (risk) {
        case '缺乏考试记录，难以准确评估能力':
          suggestions.push('建议参加模拟考试并录入成绩');
          break;
        case '缺乏工作经验可能影响竞争力':
          suggestions.push('考虑参加实习或志愿服务积累经验');
          break;
        case '技能信息不完整':
          suggestions.push('完善个人技能信息');
          break;
        case '历史考试成绩偏低':
          suggestions.push('加强基础知识学习，提升应试能力');
          break;
      }
    });

    return suggestions;
  }

  private calculateTrendBonus(examRecords: ExamRecord[]): number {
    if (examRecords.length < 2) return 0;

    const sorted = examRecords.sort((a, b) => a.examDate.getTime() - b.examDate.getTime());
    const recent = sorted.slice(-2);
    
    if (recent.length < 2) return 0;

    const improvement = recent[1].totalScore - recent[0].totalScore;
    return Math.max(0, Math.min(0.1, improvement / 100)); // 最多10%加成
  }
}
