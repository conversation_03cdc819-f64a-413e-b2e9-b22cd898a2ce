===============================================================================
                    事业单位招聘报考推荐系统 - 使用说明
===============================================================================

📋 目录
1. 系统概述
2. 环境要求
3. 安装步骤
4. 功能使用指南
5. 常见问题
6. 技术支持

===============================================================================
1. 系统概述
===============================================================================

事业单位招聘报考推荐系统是一个基于数据分析的智能推荐平台，专门解决事业单位
招聘考试中的"冷热不均"现象。系统通过多维度能力评估、智能岗位匹配和数据可视
化分析，帮助考生科学择岗，提高人岗匹配度。

核心功能：
✓ 用户认证和权限管理
✓ 考生信息管理和能力评估
✓ 岗位信息发布和管理
✓ 智能推荐算法
✓ 数据分析和可视化
✓ 报考预测和趋势分析

===============================================================================
2. 环境要求
===============================================================================

软件环境：
- 操作系统：Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- Node.js：18.0 或更高版本
- PostgreSQL：13.0 或更高版本
- 浏览器：Chrome 90+, Firefox 88+, Safari 14+, Edge 90+

硬件要求：
- 内存：至少 4GB RAM（推荐 8GB）
- 存储：至少 2GB 可用空间
- 网络：稳定的互联网连接

===============================================================================
3. 安装步骤
===============================================================================

3.1 快速安装（推荐）
-------------------
1. 下载项目文件到本地
2. 打开命令行工具，进入项目目录
3. 运行自动安装脚本：
   npm run setup

4. 创建数据库：
   createdb recruitment_recommendation

5. 初始化数据库：
   npm run init-db

6. 启动系统：
   npm run dev

7. 访问系统：
   前端：http://localhost:3000
   后端：http://localhost:5000

3.2 手动安装
-----------
如果自动安装失败，可以按以下步骤手动安装：

步骤1：安装依赖
npm install
cd client && npm install
cd ../server && npm install

步骤2：配置环境变量
复制 server/.env.example 到 server/.env
复制 client/.env.example 到 client/.env
编辑配置文件中的数据库连接信息

步骤3：数据库设置
创建PostgreSQL数据库：recruitment_recommendation
运行初始化脚本：cd server && npm run init-db

步骤4：启动服务
开发模式：npm run dev
生产模式：npm run build && npm start

===============================================================================
4. 功能使用指南
===============================================================================

4.1 用户注册和登录
-----------------
首次使用：
1. 访问 http://localhost:3000
2. 点击"立即注册"
3. 填写邮箱、密码、姓名等信息
4. 提交注册，系统自动登录

已有账号：
1. 在登录页面输入邮箱和密码
2. 点击"登录"按钮

测试账号：
邮箱：<EMAIL>
密码：admin123

4.2 考生信息管理
---------------
完善个人资料：
1. 登录后点击右上角用户头像
2. 选择"个人资料"
3. 填写基本信息：
   - 身份证号
   - 出生日期
   - 教育背景
   - 专业信息
   - 民族信息
   - 工作经验
   - 技能特长

添加考试记录：
1. 进入个人资料页面
2. 点击"考试记录"标签
3. 添加历史考试成绩：
   - 考试年份
   - 考试类型
   - 各科成绩
   - 总分和排名

4.3 岗位浏览和搜索
-----------------
浏览岗位：
1. 点击导航栏"岗位信息"
2. 查看岗位列表
3. 点击岗位标题查看详情

搜索岗位：
1. 使用搜索框输入关键词
2. 使用筛选条件：
   - 岗位类型（行政、技术、教育、医疗）
   - 岗位级别（入门、中级、高级、管理）
   - 工作地点
   - 学历要求
   - 薪资范围

4.4 智能推荐功能
---------------
获取推荐：
1. 完善个人资料和考试记录
2. 点击导航栏"智能推荐"
3. 系统自动分析并推荐匹配岗位
4. 查看推荐理由和匹配度

推荐设置：
1. 点击"刷新推荐"
2. 调整推荐参数：
   - 多样性权重
   - 新颖性权重
   - 推荐数量

查看推荐解释：
1. 点击推荐岗位的"查看详情"
2. 查看详细的推荐理由：
   - 能力匹配分析
   - 竞争度评估
   - 成功概率预测
   - 改进建议

4.5 数据分析功能
---------------
能力分析：
1. 点击导航栏"数据分析"
2. 选择"能力分析"标签
3. 查看能力画像雷达图：
   - 分析能力
   - 语言能力
   - 数值能力
   - 逻辑能力
   - 综合能力

趋势分析：
1. 选择"趋势分析"标签
2. 查看申请趋势图表
3. 调整时间周期（按周/月/季度）

竞争热度：
1. 选择"竞争热度"标签
2. 查看岗位竞争热力图
3. 了解不同地区和类型的岗位竞争情况

4.6 管理功能（管理员/HR）
-----------------------
岗位管理：
1. 使用管理员账号登录
2. 点击"管理面板"
3. 添加/编辑/删除岗位信息

用户管理：
1. 查看注册用户列表
2. 管理用户权限
3. 查看用户活动统计

数据统计：
1. 查看系统整体数据
2. 分析用户行为
3. 监控推荐效果

===============================================================================
5. 常见问题
===============================================================================

Q1：无法启动系统怎么办？
A1：请检查以下几点：
    - Node.js 版本是否为 18+
    - PostgreSQL 是否正常运行
    - 端口 3000 和 5000 是否被占用
    - 运行 npm run test:system 检查系统状态

Q2：数据库连接失败？
A2：请确认：
    - PostgreSQL 服务已启动
    - 数据库 recruitment_recommendation 已创建
    - server/.env 文件中的数据库配置正确
    - 数据库用户有足够权限

Q3：推荐功能不工作？
A3：推荐功能需要：
    - 完善的个人资料信息
    - 至少一条考试记录
    - 系统中有可用的岗位信息
    - 如果仍有问题，请查看浏览器控制台错误信息

Q4：图表显示异常？
A4：可能原因：
    - 浏览器不支持（请使用现代浏览器）
    - 网络连接问题
    - 数据加载失败
    - 刷新页面或清除浏览器缓存

Q5：忘记密码怎么办？
A5：目前系统暂未实现密码重置功能，请：
    - 使用测试账号：<EMAIL> / admin123
    - 或重新注册新账号
    - 联系管理员重置密码

Q6：系统运行缓慢？
A6：优化建议：
    - 确保系统资源充足（内存、CPU）
    - 关闭不必要的后台程序
    - 检查网络连接状态
    - 清理浏览器缓存

===============================================================================
6. 技术支持
===============================================================================

6.1 日志查看
-----------
如果遇到问题，可以查看日志文件：
- 前端：浏览器开发者工具 -> Console
- 后端：命令行窗口的输出信息
- 数据库：PostgreSQL 日志文件

6.2 测试工具
-----------
系统提供了多种测试工具：
- npm run test:system  # 系统功能测试
- npm run test:unit    # 单元测试
- npm run demo         # 功能演示
- npm run check        # 完整检查

6.3 性能监控
-----------
访问以下地址查看系统状态：
- http://localhost:5000/health  # 健康检查
- 浏览器开发者工具 -> Network   # 网络性能
- 浏览器开发者工具 -> Performance # 页面性能

6.4 备份和恢复
-------------
数据备份：
pg_dump recruitment_recommendation > backup.sql

数据恢复：
psql recruitment_recommendation < backup.sql

6.5 更新和维护
-------------
更新系统：
1. 备份数据
2. 拉取最新代码
3. 运行 npm run install:all
4. 运行 npm run build
5. 重启服务

6.6 联系方式
-----------
如果以上方法都无法解决问题，请：
- 查看项目文档：docs/ 目录
- 查看 GitHub Issues（如果有）
- 联系开发团队

===============================================================================
7. 附录
===============================================================================

7.1 系统架构
-----------
前端：React 18 + TypeScript + Ant Design + ECharts
后端：Node.js + Express + TypeORM + PostgreSQL
推荐算法：协同过滤 + 内容推荐 + 知识推荐 + 混合模型

7.2 文件结构
-----------
毕设/
├── client/          # 前端应用
├── server/          # 后端应用
├── docs/           # 文档
├── scripts/        # 工具脚本
└── 使用说明.txt     # 本文件

7.3 重要文件
-----------
- README.md                    # 项目介绍
- QUICKSTART.md               # 快速开始
- PROJECT_SUMMARY.md          # 项目总结
- docs/API.md                 # API文档
- docs/DEPLOYMENT.md          # 部署指南

===============================================================================

感谢使用事业单位招聘报考推荐系统！
如有问题，请参考文档或联系技术支持。

版本：1.0.0
更新时间：2024年
开发团队：[您的姓名]

===============================================================================
