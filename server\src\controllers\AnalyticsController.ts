import { Response } from 'express';
import { AuthRequest } from '../middleware/auth';
import { AppDataSource } from '../config/database';
import { Job } from '../entities/Job';
import { JobApplication } from '../entities/JobApplication';
import { Candidate } from '../entities/Candidate';
import { AbilityAnalysisService } from '../services/AbilityAnalysisService';
import { PredictionService } from '../services/PredictionService';

export class AnalyticsController {
  private jobRepository = AppDataSource.getRepository(Job);
  private applicationRepository = AppDataSource.getRepository(JobApplication);
  private candidateRepository = AppDataSource.getRepository(Candidate);
  private abilityService = new AbilityAnalysisService();
  private predictionService = new PredictionService();

  // 获取仪表板统计
  getDashboardStats = async (req: AuthRequest, res: Response) => {
    try {
      const userId = req.user!.id;

      // 获取考生信息
      const candidate = await this.candidateRepository.findOne({
        where: { userId },
        relations: ['applications'],
      });

      // 基础统计
      const totalJobs = await this.jobRepository.count({ where: { isActive: true } });
      const activeJobs = await this.jobRepository.count({
        where: {
          isActive: true,
          registrationEndDate: { gte: new Date() } as any
        }
      });

      const myApplications = candidate?.applications?.length || 0;

      // 推荐岗位数量（模拟）
      const recommendedJobs = Math.min(8, Math.floor(totalJobs * 0.1));

      // 最近活动数据
      const recentActivity = await this.getRecentActivityData(candidate?.id);

      res.json({
        success: true,
        data: {
          totalJobs,
          activeJobs,
          myApplications,
          recommendedJobs,
          averageMatchScore: candidate ? await this.calculateAverageMatchScore(candidate.id) : 0,
          recentActivity,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  // 获取能力画像
  getAbilityProfile = async (req: AuthRequest, res: Response) => {
    try {
      const userId = req.user!.id;

      const candidate = await this.candidateRepository.findOne({
        where: { userId },
      });

      if (!candidate) {
        return res.status(404).json({
          success: false,
          error: { message: '考生信息不存在，请先完善个人资料' },
        });
      }

      const abilityProfile = await this.abilityService.analyzeAbilityProfile(candidate.id);

      res.json({
        success: true,
        data: abilityProfile,
      });
    } catch (error) {
      throw error;
    }
  };

  // 获取申请趋势
  getApplicationTrends = async (req: AuthRequest, res: Response) => {
    try {
      const userId = req.user!.id;
      const { period = 'month' } = req.query;

      const candidate = await this.candidateRepository.findOne({
        where: { userId },
        relations: ['applications'],
      });

      if (!candidate) {
        return res.json({
          success: true,
          data: [],
          message: '暂无申请记录',
        });
      }

      const trends = await this.calculateApplicationTrends(candidate.applications || [], period as string);

      res.json({
        success: true,
        data: trends,
      });
    } catch (error) {
      throw error;
    }
  };

  // 获取系统概览
  getSystemOverview = async (req: AuthRequest, res: Response) => {
    try {
      const totalJobs = await this.jobRepository.count();
      const activeJobs = await this.jobRepository.count({ where: { isActive: true } });
      const totalApplications = await this.applicationRepository.count();
      const totalCandidates = await this.candidateRepository.count();

      // 获取热门和冷门岗位
      const { hotJobs, coldJobs } = await this.predictionService.identifyHotAndColdJobs();

      // 计算平均竞争比
      const avgCompetitionRatio = await this.calculateAverageCompetitionRatio();

      res.json({
        success: true,
        data: {
          totalJobs,
          activeJobs,
          totalApplications,
          totalCandidates,
          avgCompetitionRatio,
          hotJobs: hotJobs.slice(0, 5),
          coldJobs: coldJobs.slice(0, 5),
        },
      });
    } catch (error) {
      throw error;
    }
  };

  // 获取岗位趋势
  getJobTrends = async (req: AuthRequest, res: Response) => {
    try {
      // TODO: 实现获取岗位趋势功能
      res.json({
        success: true,
        data: [],
        message: '功能开发中',
      });
    } catch (error) {
      throw error;
    }
  };

  // 获取考生分析
  getCandidateAnalysis = async (req: AuthRequest, res: Response) => {
    try {
      // TODO: 实现获取考生分析功能
      res.json({
        success: true,
        data: null,
        message: '功能开发中',
      });
    } catch (error) {
      throw error;
    }
  };

  // 获取推荐性能
  getRecommendationPerformance = async (req: AuthRequest, res: Response) => {
    try {
      const recommendedApplications = await this.applicationRepository.find({
        where: { isRecommended: true },
        relations: ['job'],
      });

      const totalRecommended = recommendedApplications.length;
      const successfulRecommendations = recommendedApplications.filter(
        app => app.status === 'approved'
      ).length;

      const successRate = totalRecommended > 0 ? successfulRecommendations / totalRecommended : 0;
      const avgMatchScore = recommendedApplications.reduce(
        (sum, app) => sum + (app.matchScore || 0), 0
      ) / totalRecommended;

      res.json({
        success: true,
        data: {
          totalRecommended,
          successfulRecommendations,
          successRate: Math.round(successRate * 100) / 100,
          avgMatchScore: Math.round(avgMatchScore * 100) / 100,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  // 辅助方法
  private async getRecentActivityData(candidateId?: string) {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentApplications = candidateId
      ? await this.applicationRepository.find({
          where: {
            candidateId,
            createdAt: { gte: thirtyDaysAgo } as any
          },
        })
      : [];

    // 按日期分组统计
    const activityData = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      const dayApplications = recentApplications.filter(app =>
        app.createdAt.toISOString().split('T')[0] === dateStr
      ).length;

      activityData.push({
        date: dateStr,
        applications: dayApplications,
        recommendations: Math.floor(dayApplications * 0.3), // 模拟推荐数据
      });
    }

    return activityData;
  }

  private async calculateAverageMatchScore(candidateId: string): Promise<number> {
    const applications = await this.applicationRepository.find({
      where: { candidateId },
    });

    if (applications.length === 0) return 0;

    const totalScore = applications.reduce((sum, app) => sum + (app.matchScore || 0), 0);
    return Math.round((totalScore / applications.length) * 100) / 100;
  }

  private async calculateApplicationTrends(applications: any[], period: string) {
    // 按时间段分组统计申请趋势
    const trends = [];
    const now = new Date();
    const periodDays = period === 'week' ? 7 : period === 'month' ? 30 : 90;

    for (let i = 5; i >= 0; i--) {
      const endDate = new Date(now);
      endDate.setDate(endDate.getDate() - i * periodDays);
      const startDate = new Date(endDate);
      startDate.setDate(startDate.getDate() - periodDays);

      const periodApplications = applications.filter(app => {
        const appDate = new Date(app.createdAt);
        return appDate >= startDate && appDate < endDate;
      });

      trends.push({
        period: `${startDate.getMonth() + 1}/${startDate.getDate()}`,
        applications: periodApplications.length,
        avgMatchScore: periodApplications.length > 0
          ? periodApplications.reduce((sum, app) => sum + (app.matchScore || 0), 0) / periodApplications.length
          : 0,
      });
    }

    return trends;
  }

  private async calculateAverageCompetitionRatio(): Promise<number> {
    const jobs = await this.jobRepository.find({
      relations: ['applications'],
    });

    if (jobs.length === 0) return 0;

    const totalRatio = jobs.reduce((sum, job) => {
      const applications = job.applications?.length || 0;
      return sum + (applications / job.recruitmentCount);
    }, 0);

    return Math.round((totalRatio / jobs.length) * 100) / 100;
  }
}
