import { Response } from 'express';
import { AuthRequest } from '../middleware/auth';
import { AppDataSource } from '../config/database';
import { Job } from '../entities/Job';
import { JobApplication } from '../entities/JobApplication';
import { Candidate } from '../entities/Candidate';
import { AbilityAnalysisService } from '../services/AbilityAnalysisService';
import { PredictionService } from '../services/PredictionService';

export class AnalyticsController {
  private jobRepository = AppDataSource.getRepository(Job);
  private applicationRepository = AppDataSource.getRepository(JobApplication);
  private candidateRepository = AppDataSource.getRepository(Candidate);
  private abilityService = new AbilityAnalysisService();
  private predictionService = new PredictionService();

  // 获取仪表板统计
  getDashboardStats = async (req: AuthRequest, res: Response) => {
    try {
      const userId = req.user!.id;

      // 获取考生信息
      const candidate = await this.candidateRepository.findOne({
        where: { userId },
        relations: ['applications'],
      });

      // 基础统计
      const totalJobs = await this.jobRepository.count({ where: { isActive: true } });
      const activeJobs = await this.jobRepository.count({
        where: {
          isActive: true,
          registrationEndDate: { gte: new Date() } as any
        }
      });

      const myApplications = candidate?.applications?.length || 0;

      // 推荐岗位数量（模拟）
      const recommendedJobs = Math.min(8, Math.floor(totalJobs * 0.1));

      // 最近活动数据
      const recentActivity = await this.getRecentActivityData(candidate?.id);

      res.json({
        success: true,
        data: {
          totalJobs,
          activeJobs,
          myApplications,
          recommendedJobs,
          averageMatchScore: candidate ? await this.calculateAverageMatchScore(candidate.id) : 0,
          recentActivity,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  // 获取能力画像
  getAbilityProfile = async (req: AuthRequest, res: Response) => {
    try {
      const userId = req.user!.id;

      const candidate = await this.candidateRepository.findOne({
        where: { userId },
      });

      if (!candidate) {
        return res.status(404).json({
          success: false,
          error: { message: '考生信息不存在，请先完善个人资料' },
        });
      }

      const abilityProfile = await this.abilityService.analyzeAbilityProfile(candidate.id);

      res.json({
        success: true,
        data: abilityProfile,
      });
    } catch (error) {
      throw error;
    }
  };

  // 获取申请趋势
  getApplicationTrends = async (req: AuthRequest, res: Response) => {
    try {
      const userId = req.user!.id;
      const { period = 'month' } = req.query;

      const candidate = await this.candidateRepository.findOne({
        where: { userId },
        relations: ['applications'],
      });

      if (!candidate) {
        return res.json({
          success: true,
          data: [],
          message: '暂无申请记录',
        });
      }

      const trends = await this.calculateApplicationTrends(candidate.applications || [], period as string);

      res.json({
        success: true,
        data: trends,
      });
    } catch (error) {
      throw error;
    }
  };

  // 获取系统概览
  getSystemOverview = async (req: AuthRequest, res: Response) => {
    try {
      // TODO: 实现获取系统概览功能
      res.json({
        success: true,
        data: null,
        message: '功能开发中',
      });
    } catch (error) {
      throw error;
    }
  };

  // 获取岗位趋势
  getJobTrends = async (req: AuthRequest, res: Response) => {
    try {
      // TODO: 实现获取岗位趋势功能
      res.json({
        success: true,
        data: [],
        message: '功能开发中',
      });
    } catch (error) {
      throw error;
    }
  };

  // 获取考生分析
  getCandidateAnalysis = async (req: AuthRequest, res: Response) => {
    try {
      // TODO: 实现获取考生分析功能
      res.json({
        success: true,
        data: null,
        message: '功能开发中',
      });
    } catch (error) {
      throw error;
    }
  };

  // 获取推荐性能
  getRecommendationPerformance = async (req: AuthRequest, res: Response) => {
    try {
      // TODO: 实现获取推荐性能功能
      res.json({
        success: true,
        data: null,
        message: '功能开发中',
      });
    } catch (error) {
      throw error;
    }
  };
}
