#!/usr/bin/env node

const axios = require('axios');
const fs = require('fs');

const API_BASE_URL = 'http://localhost:5000/api';
let authToken = '';

console.log('🧪 开始系统功能测试...\n');

// 测试结果记录
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

// 测试工具函数
function logTest(name, passed, message = '') {
  const status = passed ? '✅ PASS' : '❌ FAIL';
  console.log(`${status} ${name}${message ? ': ' + message : ''}`);
  
  testResults.tests.push({
    name,
    passed,
    message,
    timestamp: new Date().toISOString()
  });
  
  if (passed) {
    testResults.passed++;
  } else {
    testResults.failed++;
  }
}

async function makeRequest(method, endpoint, data = null, useAuth = false) {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${endpoint}`,
      headers: {}
    };

    if (useAuth && authToken) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }

    if (data) {
      config.data = data;
      config.headers['Content-Type'] = 'application/json';
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data || error.message,
      status: error.response?.status || 500
    };
  }
}

// 测试用例
async function testHealthCheck() {
  console.log('\n📋 测试系统健康检查...');
  
  const result = await makeRequest('GET', '/health');
  logTest('健康检查接口', result.success && result.status === 200);
}

async function testUserRegistration() {
  console.log('\n👤 测试用户注册...');
  
  const testUser = {
    email: `test_${Date.now()}@example.com`,
    password: 'test123456',
    name: '测试用户',
    phone: '13800138000'
  };

  const result = await makeRequest('POST', '/auth/register', testUser);
  
  if (result.success && result.data.success) {
    authToken = result.data.data.token;
    logTest('用户注册', true, '注册成功并获取token');
  } else {
    logTest('用户注册', false, result.error?.message || '注册失败');
  }
}

async function testUserLogin() {
  console.log('\n🔐 测试用户登录...');
  
  // 使用管理员账号登录
  const loginData = {
    email: '<EMAIL>',
    password: 'admin123'
  };

  const result = await makeRequest('POST', '/auth/login', loginData);
  
  if (result.success && result.data.success) {
    authToken = result.data.data.token;
    logTest('用户登录', true, '登录成功');
  } else {
    logTest('用户登录', false, result.error?.message || '登录失败');
  }
}

async function testGetCurrentUser() {
  console.log('\n👤 测试获取当前用户信息...');
  
  const result = await makeRequest('GET', '/auth/me', null, true);
  logTest('获取用户信息', result.success && result.data.success);
}

async function testGetJobs() {
  console.log('\n💼 测试获取岗位列表...');
  
  const result = await makeRequest('GET', '/jobs', null, true);
  logTest('获取岗位列表', result.success);
}

async function testGetRecommendations() {
  console.log('\n⭐ 测试获取推荐岗位...');
  
  const result = await makeRequest('GET', '/recommendations', null, true);
  
  // 推荐功能可能因为没有考生资料而失败，这是正常的
  if (result.success || result.status === 404) {
    logTest('获取推荐岗位', true, '接口正常响应');
  } else {
    logTest('获取推荐岗位', false, result.error?.message || '接口异常');
  }
}

async function testGetAnalytics() {
  console.log('\n📊 测试数据分析接口...');
  
  const endpoints = [
    '/analytics/dashboard',
    '/analytics/ability-profile',
    '/analytics/application-trends'
  ];

  for (const endpoint of endpoints) {
    const result = await makeRequest('GET', endpoint, null, true);
    const testName = `分析接口 ${endpoint}`;
    
    // 分析接口可能因为没有数据而返回404，这是正常的
    if (result.success || result.status === 404) {
      logTest(testName, true);
    } else {
      logTest(testName, false, result.error?.message);
    }
  }
}

async function testDatabaseConnection() {
  console.log('\n🗄️ 测试数据库连接...');
  
  // 通过健康检查间接测试数据库连接
  const result = await makeRequest('GET', '/health');
  
  if (result.success && result.data.status === 'OK') {
    logTest('数据库连接', true, '通过健康检查验证');
  } else {
    logTest('数据库连接', false, '健康检查失败');
  }
}

async function testErrorHandling() {
  console.log('\n🚫 测试错误处理...');
  
  // 测试不存在的端点
  const result1 = await makeRequest('GET', '/nonexistent');
  logTest('404错误处理', result1.status === 404);
  
  // 测试未授权访问
  const result2 = await makeRequest('GET', '/auth/me');
  logTest('401错误处理', result2.status === 401);
  
  // 测试无效数据
  const result3 = await makeRequest('POST', '/auth/login', { invalid: 'data' });
  logTest('400错误处理', result3.status === 400 || result3.status === 401);
}

async function testPerformance() {
  console.log('\n⚡ 测试系统性能...');
  
  const startTime = Date.now();
  const promises = [];
  
  // 并发请求测试
  for (let i = 0; i < 10; i++) {
    promises.push(makeRequest('GET', '/health'));
  }
  
  await Promise.all(promises);
  const endTime = Date.now();
  const duration = endTime - startTime;
  
  logTest('并发性能测试', duration < 5000, `10个并发请求耗时 ${duration}ms`);
}

// 主测试流程
async function runTests() {
  try {
    await testHealthCheck();
    await testDatabaseConnection();
    await testUserLogin();
    await testGetCurrentUser();
    await testGetJobs();
    await testGetRecommendations();
    await testGetAnalytics();
    await testErrorHandling();
    await testPerformance();
    
    // 如果登录成功，测试注册功能
    if (authToken) {
      await testUserRegistration();
    }
    
  } catch (error) {
    console.error('\n❌ 测试过程中发生错误:', error.message);
  }
}

// 生成测试报告
function generateReport() {
  console.log('\n📋 测试报告');
  console.log('='.repeat(50));
  console.log(`总测试数: ${testResults.passed + testResults.failed}`);
  console.log(`通过: ${testResults.passed}`);
  console.log(`失败: ${testResults.failed}`);
  console.log(`成功率: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ 失败的测试:');
    testResults.tests
      .filter(test => !test.passed)
      .forEach(test => {
        console.log(`  - ${test.name}: ${test.message}`);
      });
  }
  
  // 保存详细报告
  const reportData = {
    summary: {
      total: testResults.passed + testResults.failed,
      passed: testResults.passed,
      failed: testResults.failed,
      successRate: ((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1) + '%'
    },
    tests: testResults.tests,
    timestamp: new Date().toISOString()
  };
  
  fs.writeFileSync('test-report.json', JSON.stringify(reportData, null, 2));
  console.log('\n📄 详细报告已保存到 test-report.json');
}

// 检查服务器是否运行
async function checkServerStatus() {
  console.log('🔍 检查服务器状态...');
  
  try {
    const result = await makeRequest('GET', '/health');
    if (result.success) {
      console.log('✅ 服务器正在运行\n');
      return true;
    }
  } catch (error) {
    console.log('❌ 服务器未运行或无法连接');
    console.log('请确保服务器已启动: npm run dev\n');
    return false;
  }
}

// 主函数
async function main() {
  const serverRunning = await checkServerStatus();
  
  if (!serverRunning) {
    process.exit(1);
  }
  
  await runTests();
  generateReport();
  
  console.log('\n🎉 测试完成!');
  
  if (testResults.failed > 0) {
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { runTests, testResults };
