{"name": "recruitment-recommendation-server", "version": "1.0.0", "description": "事业单位招聘报考推荐系统后端", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "lint": "eslint src --ext .ts", "init-db": "ts-node src/scripts/initDatabase.ts", "migration:generate": "typeorm-ts-node-commonjs migration:generate", "migration:run": "typeorm-ts-node-commonjs migration:run", "migration:revert": "typeorm-ts-node-commonjs migration:revert"}, "keywords": ["recruitment", "recommendation", "backend", "api"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "typeorm": "^0.3.17", "reflect-metadata": "^0.1.13", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "multer": "^1.4.5-lts.1", "csv-parser": "^3.0.0", "node-cron": "^3.0.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/pg": "^8.10.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/node-cron": "^3.0.11", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "typescript": "^5.3.3"}}