import { Router } from 'express';
import { AnalyticsController } from '../controllers/AnalyticsController';
import { authenticateToken, requireHR } from '../middleware/auth';

const router = Router();
const analyticsController = new AnalyticsController();

// 所有路由都需要认证
router.use(authenticateToken);

// 个人分析数据
router.get('/dashboard', analyticsController.getDashboardStats);
router.get('/ability-profile', analyticsController.getAbilityProfile);
router.get('/application-trends', analyticsController.getApplicationTrends);

// 系统分析数据（需要HR权限）
router.get('/system/overview', requireHR, analyticsController.getSystemOverview);
router.get('/system/job-trends', requireHR, analyticsController.getJobTrends);
router.get('/system/candidate-analysis', requireHR, analyticsController.getCandidateAnalysis);
router.get('/system/recommendation-performance', requireHR, analyticsController.getRecommendationPerformance);

export default router;
