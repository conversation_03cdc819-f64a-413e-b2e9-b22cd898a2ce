#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始设置事业单位招聘报考推荐系统...\n');

// 检查 Node.js 版本
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 18) {
  console.error('❌ 需要 Node.js 18 或更高版本');
  process.exit(1);
}

console.log(`✅ Node.js 版本: ${nodeVersion}`);

// 安装依赖
console.log('\n📦 安装依赖...');
try {
  execSync('npm install', { stdio: 'inherit' });
  execSync('cd client && npm install', { stdio: 'inherit' });
  execSync('cd server && npm install', { stdio: 'inherit' });
  console.log('✅ 依赖安装完成');
} catch (error) {
  console.error('❌ 依赖安装失败:', error.message);
  process.exit(1);
}

// 复制环境变量文件
console.log('\n⚙️  配置环境变量...');

const envFiles = [
  { src: 'client/.env.example', dest: 'client/.env' },
  { src: 'server/.env.example', dest: 'server/.env' }
];

envFiles.forEach(({ src, dest }) => {
  if (!fs.existsSync(dest)) {
    fs.copyFileSync(src, dest);
    console.log(`✅ 创建 ${dest}`);
  } else {
    console.log(`⚠️  ${dest} 已存在，跳过`);
  }
});

// 检查 PostgreSQL
console.log('\n🗄️  检查数据库...');
try {
  execSync('psql --version', { stdio: 'pipe' });
  console.log('✅ PostgreSQL 已安装');
} catch (error) {
  console.log('⚠️  PostgreSQL 未安装或未在 PATH 中');
  console.log('请安装 PostgreSQL 并确保可以通过命令行访问');
}

console.log('\n🎉 设置完成！');
console.log('\n下一步：');
console.log('1. 配置数据库连接信息在 server/.env 文件中');
console.log('2. 创建数据库: createdb recruitment_recommendation');
console.log('3. 运行开发服务器: npm run dev');
console.log('\n访问 http://localhost:3000 查看应用');
