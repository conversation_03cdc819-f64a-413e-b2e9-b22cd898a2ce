# 部署检查清单

## 🚀 部署前检查

### 环境准备
- [ ] Node.js 18+ 已安装
- [ ] PostgreSQL 13+ 已安装并运行
- [ ] npm 或 yarn 包管理器可用
- [ ] Git 版本控制工具可用

### 代码准备
- [ ] 代码已提交到版本控制系统
- [ ] 所有依赖已正确安装 (`npm run install:all`)
- [ ] 代码通过 lint 检查 (`npm run lint`)
- [ ] 单元测试通过 (`npm run test:unit`)
- [ ] 系统测试通过 (`npm run test:system`)

### 配置文件
- [ ] `server/.env` 文件已配置
- [ ] `client/.env` 文件已配置
- [ ] 数据库连接信息正确
- [ ] JWT密钥已设置且足够安全
- [ ] 文件上传路径已配置

### 数据库
- [ ] 数据库已创建
- [ ] 数据库用户权限正确
- [ ] 数据库初始化完成 (`npm run init-db`)
- [ ] 示例数据已导入（可选）

## 🔧 开发环境部署

### 快速启动
```bash
# 1. 克隆项目
git clone <repository-url>
cd 毕设

# 2. 安装依赖
npm run setup

# 3. 配置环境变量
cp server/.env.example server/.env
cp client/.env.example client/.env
# 编辑配置文件

# 4. 创建数据库
createdb recruitment_recommendation

# 5. 初始化数据库
npm run init-db

# 6. 启动开发服务器
npm run dev
```

### 验证步骤
- [ ] 前端可访问 (http://localhost:3000)
- [ ] 后端API可访问 (http://localhost:5000)
- [ ] 健康检查通过 (http://localhost:5000/health)
- [ ] 用户可以注册和登录
- [ ] 基本功能正常工作

## 🌐 生产环境部署

### 服务器准备
- [ ] 服务器操作系统已更新
- [ ] 防火墙已配置
- [ ] SSL证书已安装（HTTPS）
- [ ] 域名已解析到服务器
- [ ] 备份策略已制定

### 应用部署
- [ ] 代码已部署到服务器
- [ ] 生产环境配置已设置
- [ ] 数据库已配置和初始化
- [ ] 静态文件服务已配置
- [ ] 进程管理器已配置 (PM2)

### 反向代理配置 (Nginx)
```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # 前端静态文件
    location / {
        root /path/to/client/dist;
        try_files $uri $uri/ /index.html;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API代理
    location /api {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### PM2 配置
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'recruitment-api',
    script: './dist/index.js',
    cwd: './server',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 5000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
```

### 安全配置
- [ ] 环境变量中的敏感信息已保护
- [ ] 数据库访问已限制
- [ ] API限流已启用
- [ ] CORS策略已配置
- [ ] 安全头已设置
- [ ] 日志记录已配置

## 📊 监控和维护

### 监控指标
- [ ] 服务器资源使用率
- [ ] 应用响应时间
- [ ] 错误率和异常
- [ ] 数据库性能
- [ ] 用户活跃度

### 日志管理
- [ ] 应用日志轮转
- [ ] 错误日志监控
- [ ] 访问日志分析
- [ ] 安全日志审计

### 备份策略
- [ ] 数据库定期备份
- [ ] 应用代码备份
- [ ] 配置文件备份
- [ ] 用户上传文件备份

### 更新流程
- [ ] 代码更新流程
- [ ] 数据库迁移流程
- [ ] 回滚计划
- [ ] 测试验证流程

## 🧪 测试验证

### 功能测试
- [ ] 用户注册登录
- [ ] 岗位浏览搜索
- [ ] 推荐功能
- [ ] 数据分析
- [ ] 文件上传下载

### 性能测试
- [ ] 页面加载速度
- [ ] API响应时间
- [ ] 并发用户处理
- [ ] 数据库查询性能

### 安全测试
- [ ] SQL注入防护
- [ ] XSS攻击防护
- [ ] CSRF攻击防护
- [ ] 身份认证安全
- [ ] 权限控制验证

### 兼容性测试
- [ ] 主流浏览器兼容
- [ ] 移动设备适配
- [ ] 不同屏幕分辨率
- [ ] 网络环境适应

## 🚨 故障排除

### 常见问题
1. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接配置
   - 检查网络连通性

2. **API请求失败**
   - 检查服务器状态
   - 验证API端点
   - 查看错误日志

3. **前端页面空白**
   - 检查构建文件
   - 验证静态文件服务
   - 查看浏览器控制台

4. **推荐功能异常**
   - 检查数据完整性
   - 验证算法配置
   - 查看计算日志

### 紧急联系
- 开发团队：[联系方式]
- 运维团队：[联系方式]
- 数据库管理员：[联系方式]

## 📋 部署完成确认

部署完成后，请确认以下项目：

- [ ] 所有服务正常运行
- [ ] 监控系统已启用
- [ ] 备份任务已配置
- [ ] 文档已更新
- [ ] 团队已通知

**部署人员签名：** _______________

**部署时间：** _______________

**版本号：** _______________
