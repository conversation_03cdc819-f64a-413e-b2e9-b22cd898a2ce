import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { AppDataSource } from '../config/database';
import { User } from '../entities/User';
import { AuthRequest } from '../middleware/auth';
import { createError } from '../middleware/errorHandler';

export class AuthController {
  private userRepository = AppDataSource.getRepository(User);

  // 用户注册
  register = async (req: Request, res: Response) => {
    try {
      const { email, password, name, phone } = req.body;

      // 检查用户是否已存在
      const existingUser = await this.userRepository.findOne({
        where: { email },
      });

      if (existingUser) {
        throw createError('该邮箱已被注册', 400);
      }

      // 加密密码
      const hashedPassword = await bcrypt.hash(password, 12);

      // 创建用户
      const user = this.userRepository.create({
        email,
        password: hashedPassword,
        name,
        phone,
      });

      await this.userRepository.save(user);

      // 生成JWT token
      const token = jwt.sign(
        { userId: user.id },
        process.env.JWT_SECRET!,
        { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
      );

      // 移除密码字段
      const { password: _, ...userWithoutPassword } = user;

      res.status(201).json({
        success: true,
        data: {
          user: userWithoutPassword,
          token,
        },
        message: '注册成功',
      });
    } catch (error) {
      throw error;
    }
  };

  // 用户登录
  login = async (req: Request, res: Response) => {
    try {
      const { email, password } = req.body;

      // 查找用户
      const user = await this.userRepository.findOne({
        where: { email, isActive: true },
      });

      if (!user) {
        throw createError('邮箱或密码错误', 401);
      }

      // 验证密码
      const isPasswordValid = await bcrypt.compare(password, user.password);

      if (!isPasswordValid) {
        throw createError('邮箱或密码错误', 401);
      }

      // 更新最后登录时间
      user.lastLoginAt = new Date();
      await this.userRepository.save(user);

      // 生成JWT token
      const token = jwt.sign(
        { userId: user.id },
        process.env.JWT_SECRET!,
        { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
      );

      // 移除密码字段
      const { password: _, ...userWithoutPassword } = user;

      res.json({
        success: true,
        data: {
          user: userWithoutPassword,
          token,
        },
        message: '登录成功',
      });
    } catch (error) {
      throw error;
    }
  };

  // 获取当前用户信息
  getCurrentUser = async (req: AuthRequest, res: Response) => {
    try {
      const user = req.user!;
      const { password: _, ...userWithoutPassword } = user;

      res.json({
        success: true,
        data: userWithoutPassword,
      });
    } catch (error) {
      throw error;
    }
  };

  // 更新用户资料
  updateProfile = async (req: AuthRequest, res: Response) => {
    try {
      const userId = req.user!.id;
      const { name, phone } = req.body;

      const user = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!user) {
        throw createError('用户不存在', 404);
      }

      // 更新用户信息
      if (name) user.name = name;
      if (phone !== undefined) user.phone = phone;

      await this.userRepository.save(user);

      const { password: _, ...userWithoutPassword } = user;

      res.json({
        success: true,
        data: userWithoutPassword,
        message: '资料更新成功',
      });
    } catch (error) {
      throw error;
    }
  };

  // 修改密码
  changePassword = async (req: AuthRequest, res: Response) => {
    try {
      const userId = req.user!.id;
      const { currentPassword, newPassword } = req.body;

      const user = await this.userRepository.findOne({
        where: { id: userId },
      });

      if (!user) {
        throw createError('用户不存在', 404);
      }

      // 验证当前密码
      const isCurrentPasswordValid = await bcrypt.compare(
        currentPassword,
        user.password
      );

      if (!isCurrentPasswordValid) {
        throw createError('当前密码错误', 400);
      }

      // 加密新密码
      const hashedNewPassword = await bcrypt.hash(newPassword, 12);
      user.password = hashedNewPassword;

      await this.userRepository.save(user);

      res.json({
        success: true,
        message: '密码修改成功',
      });
    } catch (error) {
      throw error;
    }
  };

  // 忘记密码
  forgotPassword = async (req: Request, res: Response) => {
    try {
      // TODO: 实现忘记密码功能
      res.json({
        success: true,
        message: '重置密码邮件已发送',
      });
    } catch (error) {
      throw error;
    }
  };

  // 重置密码
  resetPassword = async (req: Request, res: Response) => {
    try {
      // TODO: 实现重置密码功能
      res.json({
        success: true,
        message: '密码重置成功',
      });
    } catch (error) {
      throw error;
    }
  };

  // 刷新token
  refreshToken = async (req: AuthRequest, res: Response) => {
    try {
      const user = req.user!;

      // 生成新的JWT token
      const token = jwt.sign(
        { userId: user.id },
        process.env.JWT_SECRET!,
        { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
      );

      const { password: _, ...userWithoutPassword } = user;

      res.json({
        success: true,
        data: {
          user: userWithoutPassword,
          token,
        },
        message: 'Token刷新成功',
      });
    } catch (error) {
      throw error;
    }
  };

  // 登出
  logout = async (req: AuthRequest, res: Response) => {
    try {
      // TODO: 可以在这里实现token黑名单功能
      res.json({
        success: true,
        message: '登出成功',
      });
    } catch (error) {
      throw error;
    }
  };
}
