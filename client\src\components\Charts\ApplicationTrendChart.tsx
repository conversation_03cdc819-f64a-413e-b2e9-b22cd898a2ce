import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import { Card, Typography, Select, Space } from 'antd';

const { Title, Text } = Typography;
const { Option } = Select;

interface TrendData {
  period: string;
  applications: number;
  avgMatchScore: number;
}

interface ApplicationTrendChartProps {
  data: TrendData[];
  loading?: boolean;
  height?: number;
  onPeriodChange?: (period: string) => void;
}

const ApplicationTrendChart: React.FC<ApplicationTrendChartProps> = ({
  data,
  loading = false,
  height = 300,
  onPeriodChange,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (!chartRef.current || loading) return;

    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
    }

    const chart = chartInstance.current;

    if (!data || data.length === 0) {
      chart.clear();
      return;
    }

    const periods = data.map(item => item.period);
    const applications = data.map(item => item.applications);
    const matchScores = data.map(item => item.avgMatchScore);

    const option: echarts.EChartsOption = {
      title: {
        text: '申请趋势分析',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999',
          },
        },
        formatter: (params: any) => {
          const period = params[0].axisValue;
          const applicationCount = params[0].value;
          const matchScore = params[1]?.value || 0;
          
          return `
            <div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${period}</div>
              <div style="margin-bottom: 2px;">
                <span style="color: #1890ff;">●</span> 申请数量: ${applicationCount}
              </div>
              <div>
                <span style="color: #52c41a;">●</span> 平均匹配度: ${matchScore.toFixed(1)}%
              </div>
            </div>
          `;
        },
      },
      legend: {
        data: ['申请数量', '平均匹配度'],
        bottom: 10,
      },
      xAxis: {
        type: 'category',
        data: periods,
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          fontSize: 11,
        },
      },
      yAxis: [
        {
          type: 'value',
          name: '申请数量',
          position: 'left',
          axisLabel: {
            formatter: '{value}',
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#e8e8e8',
              type: 'dashed',
            },
          },
        },
        {
          type: 'value',
          name: '匹配度(%)',
          position: 'right',
          axisLabel: {
            formatter: '{value}%',
          },
          min: 0,
          max: 100,
          splitLine: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: '申请数量',
          type: 'bar',
          yAxisIndex: 0,
          data: applications,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#83bff6' },
              { offset: 0.5, color: '#188df0' },
              { offset: 1, color: '#188df0' },
            ]),
          },
          emphasis: {
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#2378f7' },
                { offset: 0.7, color: '#2378f7' },
                { offset: 1, color: '#83bff6' },
              ]),
            },
          },
          barWidth: '60%',
        },
        {
          name: '平均匹配度',
          type: 'line',
          yAxisIndex: 1,
          data: matchScores,
          lineStyle: {
            color: '#52c41a',
            width: 3,
          },
          itemStyle: {
            color: '#52c41a',
            borderWidth: 2,
            borderColor: '#fff',
          },
          symbol: 'circle',
          symbolSize: 8,
          smooth: true,
        },
      ],
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true,
      },
    };

    chart.setOption(option);

    const handleResize = () => {
      chart.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [data, loading, height]);

  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, []);

  const getTrendAnalysis = () => {
    if (!data || data.length < 2) return null;

    const recent = data[data.length - 1];
    const previous = data[data.length - 2];
    
    const applicationChange = recent.applications - previous.applications;
    const scoreChange = recent.avgMatchScore - previous.avgMatchScore;

    return {
      applicationTrend: applicationChange > 0 ? 'up' : applicationChange < 0 ? 'down' : 'stable',
      scoreTrend: scoreChange > 0 ? 'up' : scoreChange < 0 ? 'down' : 'stable',
      applicationChange,
      scoreChange,
    };
  };

  const analysis = getTrendAnalysis();

  return (
    <Card>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <div>
          <Title level={4} style={{ margin: 0 }}>
            申请趋势分析
          </Title>
          <Text type="secondary">
            您的申请活动和匹配度变化趋势
          </Text>
        </div>
        {onPeriodChange && (
          <Select
            defaultValue="month"
            style={{ width: 120 }}
            onChange={onPeriodChange}
          >
            <Option value="week">按周</Option>
            <Option value="month">按月</Option>
            <Option value="quarter">按季度</Option>
          </Select>
        )}
      </div>

      <div ref={chartRef} style={{ height: `${height}px`, width: '100%' }} />

      {analysis && (
        <div style={{ marginTop: 16, padding: '12px 16px', backgroundColor: '#fafafa', borderRadius: 6 }}>
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text strong>趋势分析：</Text>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text>申请活跃度：</Text>
              <Text style={{ 
                color: analysis.applicationTrend === 'up' ? '#52c41a' : 
                       analysis.applicationTrend === 'down' ? '#f5222d' : '#666'
              }}>
                {analysis.applicationTrend === 'up' ? '↗ 上升' : 
                 analysis.applicationTrend === 'down' ? '↘ 下降' : '→ 稳定'}
                {analysis.applicationChange !== 0 && ` (${analysis.applicationChange > 0 ? '+' : ''}${analysis.applicationChange})`}
              </Text>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text>匹配度水平：</Text>
              <Text style={{ 
                color: analysis.scoreTrend === 'up' ? '#52c41a' : 
                       analysis.scoreTrend === 'down' ? '#f5222d' : '#666'
              }}>
                {analysis.scoreTrend === 'up' ? '↗ 提升' : 
                 analysis.scoreTrend === 'down' ? '↘ 下降' : '→ 稳定'}
                {analysis.scoreChange !== 0 && ` (${analysis.scoreChange > 0 ? '+' : ''}${analysis.scoreChange.toFixed(1)}%)`}
              </Text>
            </div>
          </Space>
        </div>
      )}
    </Card>
  );
};

export default ApplicationTrendChart;
