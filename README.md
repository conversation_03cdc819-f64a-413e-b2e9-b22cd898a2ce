# 事业单位招聘报考推荐系统

<div align="center">

![Logo](https://img.shields.io/badge/事业单位-招聘推荐系统-blue?style=for-the-badge)

**基于数据分析的智能招聘推荐系统**

[![React](https://img.shields.io/badge/React-18-61DAFB?logo=react)](https://reactjs.org/)
[![Node.js](https://img.shields.io/badge/Node.js-18+-339933?logo=node.js)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5-3178C6?logo=typescript)](https://www.typescriptlang.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-13+-336791?logo=postgresql)](https://www.postgresql.org/)

[快速开始](#-快速开始) • [功能特性](#-功能特性) • [技术架构](#-技术架构) • [部署指南](#-部署指南)

</div>

## 📖 项目简介

事业单位招聘报考推荐系统是一个专门针对事业单位招聘考试中存在的"冷热不均"现象而设计的智能推荐平台。系统通过多维度能力评估、智能岗位匹配和数据可视化分析，帮助考生科学择岗，提高人岗匹配度。

### 🎯 解决的问题

- **报考盲目性**：考生缺乏科学的岗位选择依据
- **冷热不均**：热门岗位过度竞争，冷门岗位无人问津
- **信息不对称**：考生对岗位要求和竞争情况了解不足
- **资源浪费**：人才选拔效果不佳，入职后不适应率高

## ✨ 功能特性

### 🔐 用户认证系统
- 安全的用户注册和登录
- 基于JWT的身份认证
- 多角色权限管理（管理员/HR/考生）

### 👤 考生信息管理
- 个人基本信息录入
- 教育背景和工作经验管理
- 多维度能力评估
- 考试记录跟踪

### 💼 岗位信息系统
- 岗位信息发布和管理
- 智能搜索和筛选
- 岗位详情展示
- 报名状态跟踪

### 🎯 智能推荐算法
- **混合推荐模型**：融合协同过滤、内容推荐、知识推荐
- **能力匹配分析**：基于项目反应理论(IRT)的能力评估
- **竞争度评估**：预测岗位竞争激烈程度
- **推荐解释**：详细的推荐理由和改进建议

### 📊 数据分析模块
- **能力画像**：多维度雷达图展示
- **趋势分析**：申请活动变化趋势
- **竞争热度**：岗位竞争情况热力图
- **预测分析**：报考成功率预测

### 📈 数据可视化
- 交互式ECharts图表
- 实时数据更新
- 响应式设计
- 多维度分析视图

## 🚀 快速开始

### 环境要求

- Node.js 18+
- PostgreSQL 13+
- npm 或 yarn

### 一键启动

```bash
# 1. 克隆项目
git clone <your-repo-url>
cd 毕设

# 2. 自动设置
npm run setup

# 3. 创建数据库
createdb recruitment_recommendation

# 4. 初始化数据库
npm run init-db

# 5. 启动开发服务器
npm run dev
```

### 访问应用

- 前端：http://localhost:3000
- 后端API：http://localhost:5000
- 健康检查：http://localhost:5000/health

### 测试账号

- 邮箱：`<EMAIL>`
- 密码：`admin123`

## 🏗️ 技术架构

### 前端技术栈
- **React 18** - 现代化前端框架
- **TypeScript** - 类型安全的JavaScript
- **Ant Design** - 企业级UI组件库
- **ECharts** - 专业数据可视化
- **Vite** - 快速构建工具

### 后端技术栈
- **Node.js + Express** - 高性能服务器
- **TypeScript** - 类型安全的后端开发
- **TypeORM** - 类型安全的数据库ORM
- **PostgreSQL** - 可靠的关系型数据库
- **JWT** - 安全的身份认证

### 推荐算法
- **协同过滤算法** - 基于用户行为
- **内容推荐算法** - 基于岗位特征
- **知识推荐算法** - 基于规则引擎
- **混合推荐模型** - 多算法融合

## 📁 项目结构

```
毕设/
├── client/                 # React前端应用
│   ├── src/
│   │   ├── components/     # 可复用组件
│   │   ├── pages/         # 页面组件
│   │   ├── services/      # API服务
│   │   ├── types/         # TypeScript类型定义
│   │   └── contexts/      # React上下文
│   ├── public/            # 静态资源
│   └── package.json
├── server/                # Node.js后端应用
│   ├── src/
│   │   ├── entities/      # 数据模型
│   │   ├── controllers/   # 控制器
│   │   ├── services/      # 业务逻辑
│   │   ├── routes/        # 路由定义
│   │   ├── middleware/    # 中间件
│   │   └── config/        # 配置文件
│   └── package.json
├── docs/                  # 项目文档
├── scripts/               # 工具脚本
└── package.json          # 根配置文件
```

## 🛠️ 开发命令

```bash
# 项目设置
npm run setup              # 初始化项目
npm run demo               # 查看功能演示

# 开发
npm run dev                # 启动开发服务器
npm run dev:client         # 仅启动前端
npm run dev:server         # 仅启动后端

# 构建
npm run build              # 构建生产版本
npm run build:client       # 构建前端
npm run build:server       # 构建后端

# 测试
npm run test               # 运行所有测试
npm run test:system        # 系统功能测试
npm run test:unit          # 单元测试

# 数据库
npm run init-db            # 初始化数据库

# 代码质量
npm run lint               # 代码检查
npm run check              # 完整检查
```

## 📊 系统演示

运行演示脚本查看系统功能：

```bash
npm run demo
```

## 🧪 测试

### 自动化测试

```bash
# 运行系统测试
npm run test:system

# 运行单元测试
npm run test:unit

# 完整测试套件
npm run test
```

### 手动测试

1. 用户注册和登录
2. 考生信息录入
3. 岗位浏览和搜索
4. 智能推荐功能
5. 数据分析和可视化

## 📚 文档

- [快速启动指南](QUICKSTART.md)
- [API接口文档](docs/API.md)
- [系统架构文档](docs/ARCHITECTURE.md)
- [部署指南](docs/DEPLOYMENT.md)
- [部署检查清单](docs/DEPLOYMENT_CHECKLIST.md)
- [项目总结](PROJECT_SUMMARY.md)

## 🚀 部署指南

### 开发环境
参考 [快速开始](#-快速开始) 部分

### 生产环境
详细部署指南请查看 [DEPLOYMENT.md](docs/DEPLOYMENT.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证

## 🙏 致谢

- [React](https://reactjs.org/) - 前端框架
- [Ant Design](https://ant.design/) - UI组件库
- [ECharts](https://echarts.apache.org/) - 数据可视化
- [Node.js](https://nodejs.org/) - 后端运行时
- [TypeORM](https://typeorm.io/) - 数据库ORM
- [PostgreSQL](https://www.postgresql.org/) - 数据库

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给它一个星标！**

Made with ❤️ by [您的姓名]

</div>
