# 事业单位招聘报考推荐系统

基于数据分析的智能报考推荐系统，帮助考生科学择岗，提高人岗匹配度。

## 项目背景

针对事业单位招聘考试中存在的"冷热不均"现象，开发智能推荐系统：
- 解决考生择岗盲目性问题
- 提高报考成功率
- 优化人才选拔效果
- 促进考试资源合理配置

## 核心功能

### 1. 考生能力评估
- 多维度能力画像构建
- 基于历史考试数据的能力分析
- 项目反应理论(IRT)应用

### 2. 智能岗位推荐
- 混合推荐算法
- 考虑岗位要求与考生能力匹配
- 民族地区特殊政策支持

### 3. 报考预测分析
- 岗位热度预测
- "过热"/"过冷"岗位识别
- 报考趋势分析

### 4. 数据可视化
- 能力画像展示
- 报考趋势图表
- 岗位竞争分析

## 技术架构

### 前端
- React 18 + TypeScript
- Vite 构建工具
- Ant Design UI组件库
- ECharts 数据可视化

### 后端
- Node.js + Express + TypeScript
- PostgreSQL 数据库
- Python 数据分析模块
- JWT 身份认证

### 推荐算法
- 协同过滤
- 内容推荐
- 知识推荐
- 混合推荐模型

## 快速开始

### 安装依赖
```bash
npm run install:all
```

### 开发环境启动
```bash
npm run dev
```

### 生产环境构建
```bash
npm run build
```

## 项目结构

```
recruitment-recommendation-system/
├── client/                 # 前端应用
│   ├── src/
│   │   ├── components/     # 组件
│   │   ├── pages/         # 页面
│   │   ├── services/      # API服务
│   │   ├── utils/         # 工具函数
│   │   └── types/         # TypeScript类型定义
├── server/                # 后端应用
│   ├── src/
│   │   ├── controllers/   # 控制器
│   │   ├── models/        # 数据模型
│   │   ├── routes/        # 路由
│   │   ├── services/      # 业务逻辑
│   │   ├── utils/         # 工具函数
│   │   └── algorithms/    # 推荐算法
├── data/                  # 数据文件
├── docs/                  # 文档
└── scripts/               # 脚本文件
```

## 开发计划

- [x] 项目初始化和架构设计
- [ ] 前端界面开发
- [ ] 后端API开发
- [ ] 数据分析模块
- [ ] 推荐系统核心算法
- [ ] 数据可视化模块
- [ ] 系统测试和优化

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
