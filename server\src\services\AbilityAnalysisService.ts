import { AppDataSource } from '../config/database';
import { Candidate } from '../entities/Candidate';
import { ExamRecord, ExamType } from '../entities/ExamRecord';

export interface AbilityProfile {
  analyticalAbility: number;
  verbalAbility: number;
  numericalAbility: number;
  logicalAbility: number;
  comprehensiveAbility: number;
  overallScore: number;
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
}

export interface ExamAnalysis {
  totalExams: number;
  averageScore: number;
  bestPerformance: {
    examName: string;
    score: number;
    date: Date;
  };
  improvementTrend: {
    period: string;
    improvement: number;
  }[];
  subjectPerformance: {
    [subject: string]: {
      average: number;
      trend: 'improving' | 'stable' | 'declining';
    };
  };
}

export class AbilityAnalysisService {
  private candidateRepository = AppDataSource.getRepository(Candidate);
  private examRecordRepository = AppDataSource.getRepository(ExamRecord);

  /**
   * 基于项目反应理论(IRT)分析考生能力
   */
  async analyzeAbilityProfile(candidateId: string): Promise<AbilityProfile> {
    const candidate = await this.candidateRepository.findOne({
      where: { id: candidateId },
      relations: ['examRecords'],
    });

    if (!candidate) {
      throw new Error('考生不存在');
    }

    const examRecords = await this.examRecordRepository.find({
      where: { candidateId },
      order: { examDate: 'DESC' },
    });

    // 如果没有考试记录，返回默认能力评估
    if (examRecords.length === 0) {
      return this.getDefaultAbilityProfile();
    }

    // 计算各项能力分数
    const abilities = this.calculateAbilities(examRecords);
    
    // 分析优势和劣势
    const analysis = this.analyzeStrengthsAndWeaknesses(abilities);
    
    // 生成改进建议
    const recommendations = this.generateRecommendations(abilities, analysis);

    return {
      ...abilities,
      overallScore: this.calculateOverallScore(abilities),
      strengths: analysis.strengths,
      weaknesses: analysis.weaknesses,
      recommendations,
    };
  }

  /**
   * 分析考试表现趋势
   */
  async analyzeExamPerformance(candidateId: string): Promise<ExamAnalysis> {
    const examRecords = await this.examRecordRepository.find({
      where: { candidateId },
      order: { examDate: 'ASC' },
    });

    if (examRecords.length === 0) {
      throw new Error('暂无考试记录');
    }

    const totalExams = examRecords.length;
    const averageScore = examRecords.reduce((sum, record) => sum + record.totalScore, 0) / totalExams;
    
    // 找出最佳表现
    const bestPerformance = examRecords.reduce((best, current) => 
      current.totalScore > best.totalScore ? current : best
    );

    // 计算改进趋势
    const improvementTrend = this.calculateImprovementTrend(examRecords);
    
    // 分析各科目表现
    const subjectPerformance = this.analyzeSubjectPerformance(examRecords);

    return {
      totalExams,
      averageScore: Math.round(averageScore * 100) / 100,
      bestPerformance: {
        examName: bestPerformance.examName,
        score: bestPerformance.totalScore,
        date: bestPerformance.examDate,
      },
      improvementTrend,
      subjectPerformance,
    };
  }

  /**
   * 预测考生在特定岗位的成功概率
   */
  async predictSuccessProbability(candidateId: string, jobRequirements: any): Promise<number> {
    const abilityProfile = await this.analyzeAbilityProfile(candidateId);
    
    // 基于能力匹配度计算成功概率
    const weights = {
      analytical: jobRequirements.analyticalWeight || 0.2,
      verbal: jobRequirements.verbalWeight || 0.2,
      numerical: jobRequirements.numericalWeight || 0.2,
      logical: jobRequirements.logicalWeight || 0.2,
      comprehensive: jobRequirements.comprehensiveWeight || 0.2,
    };

    const matchScore = 
      (abilityProfile.analyticalAbility * weights.analytical +
       abilityProfile.verbalAbility * weights.verbal +
       abilityProfile.numericalAbility * weights.numerical +
       abilityProfile.logicalAbility * weights.logical +
       abilityProfile.comprehensiveAbility * weights.comprehensive) / 100;

    // 考虑历史表现和改进趋势
    const examAnalysis = await this.analyzeExamPerformance(candidateId);
    const trendBonus = this.calculateTrendBonus(examAnalysis.improvementTrend);
    
    // 最终成功概率 = 基础匹配度 + 趋势加成
    const successProbability = Math.min(matchScore + trendBonus, 1.0);
    
    return Math.round(successProbability * 100) / 100;
  }

  private calculateAbilities(examRecords: ExamRecord[]): Omit<AbilityProfile, 'overallScore' | 'strengths' | 'weaknesses' | 'recommendations'> {
    // 使用IRT模型计算能力参数
    const recentRecords = examRecords.slice(0, 5); // 取最近5次考试
    
    let analyticalSum = 0, verbalSum = 0, numericalSum = 0, logicalSum = 0, comprehensiveSum = 0;
    let count = 0;

    recentRecords.forEach(record => {
      if (record.questionAnalysis) {
        // 基于题目分析计算各项能力
        const analysis = record.questionAnalysis;
        
        // 分析能力：基于逻辑推理和问题解决类题目
        analyticalSum += this.calculateAnalyticalScore(analysis);
        
        // 语言能力：基于语文和写作类题目
        verbalSum += this.calculateVerbalScore(record, analysis);
        
        // 数值能力：基于数学和计算类题目
        numericalSum += this.calculateNumericalScore(analysis);
        
        // 逻辑能力：基于逻辑推理题目
        logicalSum += this.calculateLogicalScore(analysis);
        
        // 综合能力：基于总体表现
        comprehensiveSum += record.totalScore;
        
        count++;
      }
    });

    if (count === 0) {
      return this.getDefaultAbilities();
    }

    return {
      analyticalAbility: Math.round((analyticalSum / count) * 100) / 100,
      verbalAbility: Math.round((verbalSum / count) * 100) / 100,
      numericalAbility: Math.round((numericalSum / count) * 100) / 100,
      logicalAbility: Math.round((logicalSum / count) * 100) / 100,
      comprehensiveAbility: Math.round((comprehensiveSum / count) * 100) / 100,
    };
  }

  private calculateAnalyticalScore(analysis: any): number {
    // 基于问题解决和分析类题目的表现
    const analyticalCategories = ['问题解决', '数据分析', '逻辑推理'];
    let totalCorrect = 0;
    let totalQuestions = 0;

    analyticalCategories.forEach(category => {
      if (analysis.categoryPerformance[category]) {
        totalCorrect += analysis.categoryPerformance[category].correct;
        totalQuestions += analysis.categoryPerformance[category].total;
      }
    });

    return totalQuestions > 0 ? (totalCorrect / totalQuestions) * 100 : 60;
  }

  private calculateVerbalScore(record: ExamRecord, analysis: any): number {
    // 结合写作分数和语言类题目表现
    const writingScore = record.writingScore || 60;
    const verbalCategories = ['语言理解', '阅读理解'];
    
    let categoryScore = 60;
    let categoryCount = 0;

    verbalCategories.forEach(category => {
      if (analysis.categoryPerformance[category]) {
        const performance = analysis.categoryPerformance[category];
        categoryScore += (performance.correct / performance.total) * 100;
        categoryCount++;
      }
    });

    const avgCategoryScore = categoryCount > 0 ? categoryScore / categoryCount : 60;
    return (writingScore * 0.6 + avgCategoryScore * 0.4);
  }

  private calculateNumericalScore(analysis: any): number {
    const numericalCategories = ['数量关系', '资料分析', '数学运算'];
    let totalCorrect = 0;
    let totalQuestions = 0;

    numericalCategories.forEach(category => {
      if (analysis.categoryPerformance[category]) {
        totalCorrect += analysis.categoryPerformance[category].correct;
        totalQuestions += analysis.categoryPerformance[category].total;
      }
    });

    return totalQuestions > 0 ? (totalCorrect / totalQuestions) * 100 : 60;
  }

  private calculateLogicalScore(analysis: any): number {
    const logicalCategories = ['判断推理', '逻辑判断'];
    let totalCorrect = 0;
    let totalQuestions = 0;

    logicalCategories.forEach(category => {
      if (analysis.categoryPerformance[category]) {
        totalCorrect += analysis.categoryPerformance[category].correct;
        totalQuestions += analysis.categoryPerformance[category].total;
      }
    });

    return totalQuestions > 0 ? (totalCorrect / totalQuestions) * 100 : 60;
  }

  private calculateOverallScore(abilities: any): number {
    const scores = [
      abilities.analyticalAbility,
      abilities.verbalAbility,
      abilities.numericalAbility,
      abilities.logicalAbility,
      abilities.comprehensiveAbility,
    ];
    
    return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length * 100) / 100;
  }

  private analyzeStrengthsAndWeaknesses(abilities: any): { strengths: string[]; weaknesses: string[] } {
    const abilityMap = {
      analyticalAbility: '分析能力',
      verbalAbility: '语言能力',
      numericalAbility: '数值能力',
      logicalAbility: '逻辑能力',
      comprehensiveAbility: '综合能力',
    };

    const scores = Object.entries(abilities).map(([key, value]) => ({
      name: abilityMap[key as keyof typeof abilityMap],
      score: value as number,
      key,
    }));

    scores.sort((a, b) => b.score - a.score);

    const strengths = scores.slice(0, 2).filter(item => item.score >= 70).map(item => item.name);
    const weaknesses = scores.slice(-2).filter(item => item.score < 60).map(item => item.name);

    return { strengths, weaknesses };
  }

  private generateRecommendations(abilities: any, analysis: any): string[] {
    const recommendations: string[] = [];

    // 基于弱项生成建议
    analysis.weaknesses.forEach((weakness: string) => {
      switch (weakness) {
        case '分析能力':
          recommendations.push('建议多练习逻辑推理和数据分析类题目');
          break;
        case '语言能力':
          recommendations.push('建议加强阅读理解和写作练习');
          break;
        case '数值能力':
          recommendations.push('建议强化数学基础和计算能力训练');
          break;
        case '逻辑能力':
          recommendations.push('建议多做判断推理和逻辑思维训练');
          break;
        case '综合能力':
          recommendations.push('建议进行全面复习，提升整体应试水平');
          break;
      }
    });

    // 基于整体水平给出建议
    const overallScore = this.calculateOverallScore(abilities);
    if (overallScore >= 80) {
      recommendations.push('整体能力优秀，建议冲击高竞争岗位');
    } else if (overallScore >= 70) {
      recommendations.push('能力水平良好，建议选择匹配度高的岗位');
    } else {
      recommendations.push('建议继续提升基础能力，选择竞争相对较小的岗位');
    }

    return recommendations;
  }

  private calculateImprovementTrend(examRecords: ExamRecord[]): { period: string; improvement: number }[] {
    if (examRecords.length < 2) {
      return [];
    }

    const trends: { period: string; improvement: number }[] = [];
    
    for (let i = 1; i < examRecords.length; i++) {
      const current = examRecords[i];
      const previous = examRecords[i - 1];
      const improvement = current.totalScore - previous.totalScore;
      
      trends.push({
        period: `${previous.examDate.getFullYear()}-${current.examDate.getFullYear()}`,
        improvement: Math.round(improvement * 100) / 100,
      });
    }

    return trends;
  }

  private analyzeSubjectPerformance(examRecords: ExamRecord[]): any {
    const subjects = ['publicBasicScore', 'professionalScore', 'writingScore', 'interviewScore'];
    const performance: any = {};

    subjects.forEach(subject => {
      const scores = examRecords
        .map(record => (record as any)[subject])
        .filter(score => score !== null && score !== undefined);

      if (scores.length > 0) {
        const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        const trend = this.calculateTrend(scores);
        
        performance[subject] = {
          average: Math.round(average * 100) / 100,
          trend,
        };
      }
    });

    return performance;
  }

  private calculateTrend(scores: number[]): 'improving' | 'stable' | 'declining' {
    if (scores.length < 2) return 'stable';
    
    const firstHalf = scores.slice(0, Math.floor(scores.length / 2));
    const secondHalf = scores.slice(Math.floor(scores.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, score) => sum + score, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, score) => sum + score, 0) / secondHalf.length;
    
    const difference = secondAvg - firstAvg;
    
    if (difference > 2) return 'improving';
    if (difference < -2) return 'declining';
    return 'stable';
  }

  private calculateTrendBonus(trends: { improvement: number }[]): number {
    if (trends.length === 0) return 0;
    
    const avgImprovement = trends.reduce((sum, trend) => sum + trend.improvement, 0) / trends.length;
    return Math.max(0, Math.min(0.1, avgImprovement / 100)); // 最多10%的加成
  }

  private getDefaultAbilityProfile(): AbilityProfile {
    return {
      analyticalAbility: 60,
      verbalAbility: 60,
      numericalAbility: 60,
      logicalAbility: 60,
      comprehensiveAbility: 60,
      overallScore: 60,
      strengths: [],
      weaknesses: [],
      recommendations: ['建议完善考试记录以获得更准确的能力评估'],
    };
  }

  private getDefaultAbilities() {
    return {
      analyticalAbility: 60,
      verbalAbility: 60,
      numericalAbility: 60,
      logicalAbility: 60,
      comprehensiveAbility: 60,
    };
  }
}
