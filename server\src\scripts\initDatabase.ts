import 'reflect-metadata';
import { AppDataSource } from '../config/database';
import { User, UserRole } from '../entities/User';
import { Job, JobType, JobLevel } from '../entities/Job';
import { Education, EthnicGroup } from '../entities/Candidate';
import bcrypt from 'bcryptjs';

async function initDatabase() {
  try {
    console.log('🔄 初始化数据库连接...');
    await AppDataSource.initialize();
    console.log('✅ 数据库连接成功');

    // 创建管理员用户
    const userRepository = AppDataSource.getRepository(User);
    const existingAdmin = await userRepository.findOne({
      where: { email: '<EMAIL>' }
    });

    if (!existingAdmin) {
      console.log('🔄 创建管理员用户...');
      const hashedPassword = await bcrypt.hash('admin123', 12);
      const admin = userRepository.create({
        email: '<EMAIL>',
        password: hashedPassword,
        name: '系统管理员',
        role: UserRole.ADMIN,
      });
      await userRepository.save(admin);
      console.log('✅ 管理员用户创建成功');
      console.log('📧 邮箱: <EMAIL>');
      console.log('🔑 密码: admin123');
    } else {
      console.log('ℹ️  管理员用户已存在');
    }

    // 创建示例岗位
    const jobRepository = AppDataSource.getRepository(Job);
    const existingJobs = await jobRepository.count();

    if (existingJobs === 0) {
      console.log('🔄 创建示例岗位...');
      
      const sampleJobs = [
        {
          title: '黔南州教育局中学教师',
          department: '黔南州教育局',
          location: '都匀市',
          type: JobType.EDUCATION,
          level: JobLevel.INTERMEDIATE,
          description: '负责中学语文教学工作，制定教学计划，组织课堂教学活动。',
          requirements: '1. 本科及以上学历，师范类专业优先；2. 具有教师资格证；3. 普通话二级甲等以上；4. 热爱教育事业，责任心强。',
          recruitmentCount: 5,
          minEducation: Education.BACHELOR,
          preferredMajors: ['汉语言文学', '教育学', '中文'],
          requiredSkills: ['教学能力', '沟通能力', '组织能力'],
          ethnicPreference: true,
          preferredEthnicGroups: [EthnicGroup.MIAO, EthnicGroup.BUYI],
          minSalary: 4000,
          maxSalary: 8000,
          registrationStartDate: new Date('2024-03-01'),
          registrationEndDate: new Date('2024-03-15'),
          examDate: new Date('2024-04-20'),
        },
        {
          title: '黔南州财政局会计',
          department: '黔南州财政局',
          location: '都匀市',
          type: JobType.ADMINISTRATIVE,
          level: JobLevel.ENTRY,
          description: '负责财政资金管理、会计核算、财务报表编制等工作。',
          requirements: '1. 本科及以上学历，会计、财务管理等相关专业；2. 具有会计从业资格证；3. 熟练使用财务软件；4. 工作细致认真。',
          recruitmentCount: 3,
          minEducation: Education.BACHELOR,
          preferredMajors: ['会计学', '财务管理', '经济学'],
          requiredSkills: ['财务分析', '会计核算', 'Excel操作'],
          ethnicPreference: false,
          minSalary: 3500,
          maxSalary: 7000,
          registrationStartDate: new Date('2024-03-01'),
          registrationEndDate: new Date('2024-03-15'),
          examDate: new Date('2024-04-20'),
        },
        {
          title: '黔南州人民医院护士',
          department: '黔南州人民医院',
          location: '都匀市',
          type: JobType.MEDICAL,
          level: JobLevel.ENTRY,
          description: '负责病房护理工作，协助医生进行医疗活动，关爱患者身心健康。',
          requirements: '1. 大专及以上学历，护理学专业；2. 具有护士执业资格证；3. 身体健康，能够胜任护理工作；4. 具有爱心和责任心。',
          recruitmentCount: 10,
          minEducation: Education.COLLEGE,
          preferredMajors: ['护理学', '临床医学'],
          requiredSkills: ['护理技能', '沟通能力', '应急处理'],
          ethnicPreference: true,
          preferredEthnicGroups: [EthnicGroup.MIAO, EthnicGroup.BUYI, EthnicGroup.DONG],
          minSalary: 3000,
          maxSalary: 6000,
          registrationStartDate: new Date('2024-03-01'),
          registrationEndDate: new Date('2024-03-15'),
          examDate: new Date('2024-04-20'),
        },
        {
          title: '黔南州科技局信息技术专员',
          department: '黔南州科技局',
          location: '都匀市',
          type: JobType.TECHNICAL,
          level: JobLevel.INTERMEDIATE,
          description: '负责信息系统维护、软件开发、网络管理等技术工作。',
          requirements: '1. 本科及以上学历，计算机相关专业；2. 熟练掌握编程语言；3. 具有系统分析和设计能力；4. 了解网络安全知识。',
          recruitmentCount: 2,
          minEducation: Education.BACHELOR,
          preferredMajors: ['计算机科学与技术', '软件工程', '信息管理'],
          requiredSkills: ['编程能力', '系统分析', '网络管理'],
          ethnicPreference: false,
          minSalary: 5000,
          maxSalary: 10000,
          registrationStartDate: new Date('2024-03-01'),
          registrationEndDate: new Date('2024-03-15'),
          examDate: new Date('2024-04-20'),
        }
      ];

      for (const jobData of sampleJobs) {
        const job = jobRepository.create(jobData);
        await jobRepository.save(job);
      }

      console.log('✅ 示例岗位创建成功');
    } else {
      console.log('ℹ️  岗位数据已存在');
    }

    console.log('\n🎉 数据库初始化完成！');
    console.log('\n📊 数据统计:');
    console.log(`👥 用户数量: ${await userRepository.count()}`);
    console.log(`💼 岗位数量: ${await jobRepository.count()}`);

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
  } finally {
    await AppDataSource.destroy();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase();
}

export { initDatabase };
