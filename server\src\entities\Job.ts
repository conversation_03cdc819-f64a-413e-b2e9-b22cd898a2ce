import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { IsNotEmpty, IsNumber, Min } from 'class-validator';
import { JobApplication } from './JobApplication';
import { Education, EthnicGroup } from './Candidate';

export enum JobType {
  ADMINISTRATIVE = 'administrative',
  TECHNICAL = 'technical',
  EDUCATION = 'education',
  MEDICAL = 'medical',
  OTHER = 'other',
}

export enum JobLevel {
  ENTRY = 'entry',
  INTERMEDIATE = 'intermediate',
  SENIOR = 'senior',
  MANAGEMENT = 'management',
}

@Entity('jobs')
export class Job {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @IsNotEmpty()
  title: string;

  @Column()
  @IsNotEmpty()
  department: string;

  @Column()
  @IsNotEmpty()
  location: string;

  @Column({
    type: 'enum',
    enum: JobType,
  })
  type: JobType;

  @Column({
    type: 'enum',
    enum: JobLevel,
  })
  level: JobLevel;

  @Column({ type: 'text' })
  @IsNotEmpty()
  description: string;

  @Column({ type: 'text' })
  @IsNotEmpty()
  requirements: string;

  @Column()
  @IsNumber()
  @Min(1)
  recruitmentCount: number;

  @Column({
    type: 'enum',
    enum: Education,
  })
  minEducation: Education;

  @Column({ type: 'simple-array', nullable: true })
  preferredMajors: string[];

  @Column({ type: 'simple-array', nullable: true })
  requiredSkills: string[];

  @Column({ type: 'simple-array', nullable: true })
  preferredCertifications: string[];

  // 民族政策相关
  @Column({ default: false })
  ethnicPreference: boolean;

  @Column({ type: 'simple-array', nullable: true })
  preferredEthnicGroups: EthnicGroup[];

  // 薪资范围
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  minSalary: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  maxSalary: number;

  // 能力要求权重
  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0.2 })
  analyticalWeight: number;

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0.2 })
  verbalWeight: number;

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0.2 })
  numericalWeight: number;

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0.2 })
  logicalWeight: number;

  @Column({ type: 'decimal', precision: 3, scale: 2, default: 0.2 })
  comprehensiveWeight: number;

  // 报名和考试时间
  @Column({ type: 'timestamp' })
  registrationStartDate: Date;

  @Column({ type: 'timestamp' })
  registrationEndDate: Date;

  @Column({ type: 'timestamp' })
  examDate: Date;

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @OneToMany(() => JobApplication, (application) => application.job)
  applications: JobApplication[];
}
